{"env": {"browser": true, "commonjs": true, "es6": true, "node": true}, "extends": ["plugin:react/recommended", "airbnb", "plugin:prettier/recommended"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": [2, {"args": "none"}], "camelcase": [0], "class-methods-use-this": [0], "consistent-return": [0], "func-names": [0], "guard-for-in": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/no-cycle": [0], "import/no-default-export": [0], "import/no-extraneous-dependencies": [0], "import/no-named-as-default": [0], "import/no-restricted-exports": [0], "import/no-unresolved": [0], "import/no-useless-path-segments": [0], "import/prefer-default-export": [0], "jsx-a11y/anchor-is-valid": [0], "jsx-a11y/click-events-have-key-events": [0], "jsx-a11y/no-noninteractive-element-interactions": "off", "jsx-a11y/no-redundant-roles": "off", "jsx-a11y/no-static-element-interactions": "off", "new-cap": [0], "no-alert": "off", "no-async-promise-executor": [0], "no-buffer-constructor": [0], "no-else-return": [0], "no-nested-ternary": [0], "no-param-reassign": [0], "no-plusplus": [0], "no-prototype-builtins": [0], "no-restricted-exports": [0], "no-restricted-globals": [0], "no-restricted-syntax": [0], "no-shadow": [0], "no-underscore-dangle": [0], "no-use-before-define": "off", "prefer-destructuring": [0], "prefer-promise-reject-errors": [0], "prefer-rest-params": [0], "prettier/prettier": ["error", {"endOfLine": "auto"}], "react/default-props-match-prop-types": [0], "react/destructuring-assignment": [0], "react/forbid-prop-types": [0], "react/function-component-definition": "off", "react/jsx-filename-extension": [0], "react/jsx-fragments": [0], "react/jsx-one-expression-per-line": [0], "react/jsx-props-no-spreading": [0], "react/jsx-wrap-multilines": [0], "react/no-array-index-key": [0], "react/no-danger": [0], "react/no-find-dom-node": [0], "react/no-this-in-sfc": [0], "react/no-unstable-nested-components": "off", "react/prop-types": [0], "react/react-in-jsx-scope": [0], "react/require-default-props": [0]}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}, "typescript": {"project": ["./tsconfig.json"]}}}}