/* eslint-disable */
const {
  utils: { getPackages },
} = require('@commitlint/config-lerna-scopes')

module.exports = {
  extends: [
    'squash-pr',
    '@commitlint/config-conventional',
    '@commitlint/config-lerna-scopes',
  ],
  rules: {
    'body-max-length': [0],
    'footer-max-length': [0],
    'header-max-length': [0],
    'scope-enum': async (ctx) => [
      2,
      'always',
      [...(await getPackages(ctx)), 'root'],
    ],
  },
}
