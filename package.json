{"name": "rased", "version": "1.0.0", "description": "rased", "license": "MIT", "private": true, "author": {"name": "GeoTech team", "url": "http://gt.com.sa"}, "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js'", "build": "SKIP_PREFLIGHT_CHECK=true CI=false craco build", "dev": "cross-env PORT=7493 craco start", "test": "craco test", "eject": "craco eject", "lint": "eslint . --ext .js,.tsx,.ts", "format": "prettier --write \"./**/*.{ts,tsx,js,json,md}\"", "check:format": "prettier --list-different \"./**/*.{ts,tsx,js,json,md}\"", "docker:dev": "docker build . -t gcr.io/rased-cloud/web-app:dev", "pre-push": "yarn check:format", "cz": "git-cz", "lint:fix": "eslint . --ext .js,.ts,.tsx --fix", "commit": "commit", "gcp-login": "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/gcloud-service-account-key.json && npx google-artifactregistry-auth"}, "dependencies": {"@apollo/client": "3.4.8", "@flint/core": "^0.3.0-beta.22", "@flint/graphql": "^0.3.0-beta.22", "@flint/rjsf": "^0.3.0-beta.46", "@fullstory/browser": "^2.0.5", "@hotjar/browser": "^1.0.9", "@material-ui/lab": "^4.0.0-alpha.57", "@monaco-editor/react": "^4.3.1", "@sentry/react": "^7.70.0", "@sentry/tracing": "^6.19.7", "apollo-upload-client": "^16.0.0", "axios": "^0.24.0", "browser-image-compression": "^2.0.2", "graphql": "15.5.1", "history": "^5.0.0", "jsonpath": "^1.1.1", "ol": "7.5.2", "postcss": "^8.4.14", "react": "^17.0.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.3.0", "web-vitals": "^1.0.1"}, "devDependencies": {"@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^8.3.4", "@commitlint/config-lerna-scopes": "^13.1.0", "@commitlint/prompt-cli": "^8.3.5", "@craco/craco": "^6.4.0", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^18.0.9", "@types/react-leaflet": "^2.8.1", "@types/socket.io-client": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.20.0", "clsx": "^1.1.1", "commitlint-config-squash-pr": "^1.0.1", "craco-babel-loader": "^1.0.3", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.2.0", "eslint": "^8.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^4.2.5", "lerna": "^3.22.0", "node-polyfill-webpack-plugin": "^1.1.4", "prettier": "^2.0.5", "prettier-eslint": "^13.0.0", "pretty-quick": "^2.0.1", "react-scripts": "^5.0.0", "sass": "^1.34.0", "source-map-explorer": "^2.5.2", "typescript": "4.1.6"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}