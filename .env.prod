REACT_APP_DEPLOYMENT=PRODUCTION
# base and api endpoints
REACT_APP_BASE_URL=https://rased.tataba.ai
REACT_APP_GRAPHQL_URL=https://api.tataba.ai/graphql
REACT_APP_WEB_PORT=7493
# openId connect authentication
REACT_APP_ACCOUNTS_BASE_URL=https://accounts.gt.com.sa
REACT_APP_AUTH_DOMAIN=https://auth.gt.com.sa
REACT_APP_AUTH_WEB_CALLBACK_URL=https://rased.tataba.ai/auth/callback
REACT_APP_AUTH_WEB_CLIENT_ID=rased_web
REACT_APP_AUTH_AUTHORIZE_PATH=/oauth2/auth
REACT_APP_AUTH_TOKEN_PATH=/oauth2/token
REACT_APP_AUTH_REVOKE_PATH=/oauth2/revoke
REACT_APP_AUTH_BODY_FORMAT=form
REACT_APP_AUTH_AUTHORIZATION_METHOD=body
REACT_APP_AUTH_SCOPE=openid,offline
# third party configuration
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyAXiGcDJpUASiSo6VGpOUEodY-Fc0TmrcY
REACT_APP_SENTRY_DSN=https://<EMAIL>/****************
SKIP_PREFLIGHT_CHECK=true
# Tataba Rasd Configuration
REACT_APP_ORGANIZATION_ID=54

# graphql endpoints
REACT_APP_ORGANIZATION_GQL=https://api.tataba.dev.geotech.run/graphql
REACT_APP_OFFERS_GQL=https://vd-api.tataba.ai/offers-gql
REACT_APP_POI_GQL=https://vd-api.tataba.ai/poi-gql
REACT_APP_GENERAL_GQL=https://vd-api.tataba.ai/graphql
REACT_APP_SIMPLE_GQL=https://vd-api.tataba.ai/simple-project-gql

# wms urls
REACT_APP_WMS_URL=https://api.tataba.ai/proxy/organization-wms/
REACT_APP_WMS_URL_OFFERS=https://vd-api.tataba.ai/proxy/offers/wms
REACT_APP_WMS_URL_POI=https://vd-api.tataba.ai/proxy/poi/wms
REACT_APP_WMS_URL_VD=https://vd-api.tataba.ai/proxy/offers/wms
REACT_APP_WMS_URL_SIMPLE=https://vd-api.tataba.ai/proxy/simple_project/wms
