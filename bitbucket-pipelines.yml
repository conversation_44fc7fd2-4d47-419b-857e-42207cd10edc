image: atlassian/default-image:3

definitions:
  steps:
    - step: &lint
        name: Run lint
        script:
          # Assign created file to GOOGLE_APPLICATION_CREDENTIALS variable to be used by artifactregistry-login
          - export GOOGLE_APPLICATION_CREDENTIALS=gcloud-service-account-key.json
          # refresh auth token
          - npx google-artifactregistry-auth
          # install build packages
          - npm install

          - npm run lint
        caches:
          - node
    - step: &CheckBuild
        name: Check Build
        script:
          # Assign created file to GOOGLE_APPLICATION_CREDENTIALS variable to be used by artifactregistry-login
          - export GOOGLE_APPLICATION_CREDENTIALS=gcloud-service-account-key.json
          # refresh auth token
          - npx google-artifactregistry-auth
          # install build packages
          - npm install

          - npm run build
        caches:
          - node

    - step: &CheckDockerBuild
        name: Check Docker Build
        size: 2x
        script:
          - docker build .
        services:
          - docker

        caches:
          - docker

pipelines:
  default:
    - step: *CheckBuild
