## Muthamen

## Contents

- [Get Started](#-get-started)
  - [Donwload From BitBucket](#-download-from-bitbucket)
  - [Insall all dependencies](#-insall-all-dependencies)
  - [Install ios PODs](#-Install-ios-PODs)
  - [Install npm packages](#-Install-npm-packages)
  - [Setup environment variables](#-Setup-environment-variables)
  - [Run the application](#-Run-the-application)
- [git- lint commit messages](#-git-lint-commit-messages)
  - [Types](#-Types)
  - [Scopes:](#-Scopes)
- [Available Package Scripts](#-Available-Package-Scripts)
- [Useful resources](#-Useful-resources)

**Note: Don't use yarn install or npm install to install dependencies inside project packages because you will get error when the package manager looks-up for local packages**

Learn how to install a new package [Install Packages](#-Install-npm-packages)

## Get started:

### Download from BitBucket

```bash
$ <NAME_EMAIL>:geotech-workspace/muthamen-app.git
```

```bash
$ cd muthamen
```

### Insall all dependencies

```bash
$ yarn install # to install dependencies needed in the development such as lerna,husky, and @commitlint/cli and so on.
$ yarn bootstrap
```

### Install ios PODs

```bash
$ yarn pods:install
# or
$ cd platforms/mobile/ios
```

### Install npm packages

```bash
# add a dependency - add a dependency in a specific package [web,mobile,components,core]
yarn lerna add react --scope=web

# add a dependency to all packages  [web,mobile,components,core]
yarn lerna add axios
```

### Setup environment variables

```bash
# copy platforms/web/.env.example to platforms/web/.env with proper values
cp platforms/web/.env.example platforms/web/.env
```

### Run the application

```bash

# for web
$ yarn web

# for mobile -- start react native server
$ yarn mobile

# for android
$ yarn android

# for ios
$ yarn ios

```

## git Lint commit messages

To commit new changes you have not to use `git commit` but use `yarn cm` instead.

How does this work?

You would be asked to enter the following values.

### Type:

Must be one of the following:

- build: Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)
- ci: Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)
- docs: Documentation only changes
- feat: A new feature
- fix: A bug fix
- perf: A code change that improves performance
- refactor: A code change that neither fixes a bug nor adds a feature
- style: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- test: Adding missing tests or correcting existing tests

### Scopes:

The scope should be the name of the package affected:

- root
- web
- components
- core

## Available Package Scripts

**Example**

```bash
$ yarn ios
```

| Name           | Description                                                                                                                                                      |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `web`          | Starts web server                                                                                                                                                |
| `andorid`      | Run on andorid emulator > the andorid emulator must be pre-starte                                                                                                |
| `ios`          | Run on IOS emulator                                                                                                                                              |
| `commit`       | Open an interactive dialogue to generate a conventional commit message                                                                                           |
| `cz`           | Another way for interactive dialogue to generate a conventional commit message                                                                                   |
| `lerna`        | Start the lerna                                                                                                                                                  |
| `format`       | Prettify code using prettier                                                                                                                                     |
| `check:format` | Prints the filenames of files that are different from Prettier formatting                                                                                        |
| `bootstrap`    | Bootstrap the packages in the current Lerna repo. Installs all of their dependencies and links any cross-dependencies. nables integration with `Yarn Workspaces` |
| `lint`         | Run `eslint`                                                                                                                                                     |
| `lint:fix`     | Auto fix eslint`                                                                                                                                                 |
| `clean`        | Exec lerna clean and every clean command in the sub-packages`                                                                                                    |
| `build`        | Run build command in all the sub-pacakges`                                                                                                                       |
| `lerna`        | Run lerna cli                                                                                                                                                    |
| `pods:install` | Install ios pods                                                                                                                                                 |

## File Structure

```ts
/**
 *   └── components
        ├── ComponentName
        │   ├── hooks
        │   │   ├── index.ts
        │   │   └── useComponentName.ts
        │   ├── ComponentName.style.ts
        │   ├── ComponentName.scss
        │   ├── ComponentName.tsx
        │   ├── ComponentName.interface.ts
        │   ├── ComponentName.stories.tsx
        │   └── index.ts
 * /
```

## Useful resources

- [React-Native: Setting up the development environment](https://reactnative.dev/docs/environment-setup)
- [Tutorial: How to share code between iOS, Android & Web using React Native, react-native-web and monorepo](https://dev.to/brunolemos/tutorial-100-code-sharing-between-ios-android--web-using-react-native-web-andmonorepo-4pej)
- [Lerna](https://lerna.js.org/)
- [TypeScript in 5 minutes](https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html)
- [Custom Fonts in React Native for iOS & Android Builds](https://dev.to/kennymark/using-custom-fonts-in-react-native-21j7)
  [React Native Web Series](https://www.youtube.com/playlist?list=PLN3n1USn4xll9wq0rw0ECrO0j2PFzuXtn)
- [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0-beta.4/)

layer:
/layer/{layer_id}/
/layer/{layer_id}/{page_no}/

record details:
/record/{record_id}/

to highlight record in table without opening it:
/layer/{layer_id}/{page_no}/?highlight={record_id}
