{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es6", "DOM"], "moduleResolution": "node", "noEmit": true, "strict": false, "target": "esnext", "typeRoots": ["@types/*", "node_modules/@types"], "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "baseUrl": "./src", "rootDir": "./src", "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}