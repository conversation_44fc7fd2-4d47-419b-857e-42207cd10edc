FROM node:16-alpine as build

RUN apk add --no-cache git

ENV APP_DIR /app


WORKDIR ${APP_DIR}

COPY .npmrc .
COPY package.json .
COPY yarn.lock .
COPY .env .

COPY gcloud-service-account-key.json .

# Google cloud
ENV GOOGLE_APPLICATION_CREDENTIALS=gcloud-service-account-key.json

# refresh auth token
RUN npx google-artifactregistry-auth

RUN yarn install

COPY . .

RUN yarn build

# Stage 2
FROM nginx:1.21-alpine

COPY --from=build /app/build /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]