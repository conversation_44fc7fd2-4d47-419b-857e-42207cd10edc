export * from './common'
export * from './file.utils'
export * from './general.utils'
export * from './tasks.utils'

/**
 * Transforms search field configuration into GraphQL filter field format
 * @param root - The root object (e.g., "source_properties", "data")
 * @param jsonpath - JSONPath expression (e.g., "$.BuildingNo", "$.rased.status")
 * @returns GraphQL field format (e.g., "source_properties__BuildingNo", "data__rased__status")
 */
export const transformToGraphQLField = (
  root: string,
  jsonpath: string
): string => {
  // Remove the leading $. from JSONPath
  const pathWithoutRoot = jsonpath.replace(/^\$\./, '')

  // Replace dots with double underscores for GraphQL field format
  const graphqlPath = pathWithoutRoot.replace(/\./g, '__')

  // Combine root with path
  return `${root}__${graphqlPath}`
}

/**
 * Gets search field options from configuration
 * @param configuration - The configuration object containing searchFields
 * @returns Array of search field options for dropdown
 */
export const getSearchFieldOptions = (configuration: any) => {
  if (!configuration?.searchFields) return []

  return configuration.searchFields.map((field: any) => ({
    value: transformToGraphQLField(field.root, field.jsonpath),
    label: field.title,
    root: field.root,
    jsonpath: field.jsonpath,
  }))
}
