{"ui:order": ["quality_and_verification", "building_facts", "offer_data", "form_category"], "quality_and_verification": {"is_duplicated_image": {"ui:widget": "YesNoWidget"}, "image_covers_building": {"ui:widget": "YesNoWidget"}}, "building_facts": {"is_there_commercial_shops": {"ui:widget": "YesNoWidget"}, "building_is_commercial": {"ui:widget": "YesNoWidget"}, "building_is_under_construction": {"ui:widget": "YesNoWidget"}, "building_is_empty_land": {"ui:widget": "YesNoWidget"}, "ui:order": ["is_there_commercial_shops", "floors_count", "shop_names", "building_is_commercial", "floors_count_non_commercial", "building_is_under_construction", "floors_count_construnction", "building_is_empty_land"]}, "offer_data": {"is_there_real_estate_offer": {"ui:widget": "YesNoWidget"}}}