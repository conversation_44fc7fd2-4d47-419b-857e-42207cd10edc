{"ui:order": ["interface", "infrastructure", "setbacks", "parking", "entrances"], "interface": {"ui:order": ["has_building", "building_details", "facades_count", "commercial_units_count", "housing_providers_count"], "has_building": {"ui:widget": "YesNoWidget"}, "building_details": {"ui:order": ["building_image", "external_units", "building_floors", "has_multiple_facades", "facades_count", "has_commercial_units", "commercial_units_count", "has_housing_providers", "housing_providers_count"], "building_image": {"ui:widget": "FileWidget"}, "has_multiple_facades": {"ui:widget": "YesNoWidget"}, "has_commercial_units": {"ui:widget": "YesNoWidget"}, "has_housing_providers": {"ui:widget": "YesNoWidget"}}}, "infrastructure": {"ui:order": ["has_electricity_meter", "electricity_details", "has_water_meter", "water_details"], "has_electricity_meter": {"ui:widget": "YesNoWidget"}, "electricity_details": {"ui:order": ["electricity_meter_image", "electricity_meter_info"], "electricity_meter_image": {"ui:widget": "FileWidget"}, "electricity_meter_info": {"ui:widget": "FileWidget"}}, "has_water_meter": {"ui:widget": "YesNoWidget"}, "water_details": {"ui:order": ["water_meter_image", "water_meter_info"], "water_meter_image": {"ui:widget": "FileWidget"}, "water_meter_info": {"ui:widget": "FileWidget"}}}, "setbacks": {"ui:order": ["has_setback", "setback_directions_count", "setback_details"], "has_setback": {"ui:widget": "YesNoWidget"}, "setback_details": {"ui:order": ["setback_direction", "is_accessible"], "ui:itemTitle": "ارتداد", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة ارتداد", "items": {"is_accessible": {"ui:widget": "YesNoWidget"}}}}, "parking": {"ui:order": ["front_parking", "front_parking_details", "underground_parking", "underground_parking_details", "rear_parking", "rear_parking_details"], "front_parking": {"ui:widget": "YesNoWidget"}, "front_parking_details": {"ui:order": ["parking_count", "parking_images"], "parking_images": {"ui:widget": "FileWidget"}}, "underground_parking": {"ui:widget": "YesNoWidget"}, "underground_parking_details": {"ui:order": ["parking_count", "parking_length", "parking_width", "parking_images"], "parking_images": {"ui:widget": "FileWidget"}}, "rear_parking": {"ui:widget": "YesNoWidget"}, "rear_parking_details": {"ui:order": ["parking_count", "parking_images"], "parking_images": {"ui:widget": "FileWidget"}}}, "entrances": {"ui:order": ["entrance_count", "entrance_images", "has_corridors", "corridor_details", "has_elevators", "elevator_details"], "entrance_count": {"ui:widget": "updown"}, "entrance_images": {"ui:widget": "AddPhotoToGallery"}, "has_corridors": {"ui:widget": "YesNoWidget"}, "corridor_details": {"ui:itemTitle": "م<PERSON><PERSON>", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة ممر", "items": {"ui:order": ["corridor_number", "corridor_length", "corridor_width", "corridor_images"], "corridor_images": {"ui:widget": "AddPhotoToGallery"}}}, "has_elevators": {"ui:widget": "YesNoWidget", "ui:title": "هل يوجد مصاعد؟"}, "elevator_details": {"ui:itemTitle": "مصعد", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة مصعد", "items": {"ui:order": ["elevator_number", "elevator_length", "elevator_width", "elevator_images"], "elevator_images": {"ui:widget": "AddPhotoToGallery"}}}}}