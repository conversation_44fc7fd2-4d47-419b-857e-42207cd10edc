{"$schema": "http://json-schema.org/draft-07/schema#", "title": "النموذج الثاني", "type": "object", "definitions": {"yes_no": {"enum": ["yes", "no"], "enumNames": ["نعم", "لا"]}, "corridor_details": {"title": "تفاصيل الممر", "type": "object", "properties": {"corridor_number": {"type": "integer", "title": "رقم الممر"}, "corridor_length": {"type": "number", "title": "طول الممر"}, "corridor_width": {"type": "number", "title": "عر<PERSON> الم<PERSON>ر"}, "corridor_images": {"type": "array", "title": "صور الممر", "items": {"type": "string", "format": "uri"}}}, "required": ["corridor_number", "corridor_length", "corridor_width", "corridor_images"]}, "elevator_details": {"title": "تفاصيل المصعد", "type": "object", "properties": {"elevator_number": {"type": "integer", "title": "رقم المصعد"}, "elevator_length": {"type": "number", "title": "طول المصعد"}, "elevator_width": {"type": "number", "title": "عرض المصعد"}, "elevator_images": {"type": "array", "title": "صور المصعد", "items": {"type": "string", "format": "uri"}}}, "required": ["elevator_number", "elevator_length", "elevator_width", "elevator_images"]}, "front_parking_details": {"title": "تفاصيل المواقف في واجهة المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}, "underground_parking_details": {"title": "تفاصيل المواقف في أسفل المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_length": {"type": "number", "title": "طول المواقف"}, "parking_width": {"type": "number", "title": "عرض المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_length", "parking_width", "parking_images"]}, "rear_parking_details": {"title": "تفاصيل المواقف في خلف المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}, "building_details": {"title": "تفاصيل المبنى", "type": "object", "allOf": [{"if": {"properties": {"has_multiple_facades": {"const": "yes"}}}, "then": {"properties": {"facades_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الواجهات"}}}}, {"if": {"properties": {"has_commercial_units": {"const": "yes"}}}, "then": {"properties": {"commercial_units_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الواجهات"}}}}, {"if": {"properties": {"has_housing_providers": {"const": "yes"}}}, "then": {"properties": {"housing_providers_count": {"type": "integer", "title": "كم عدد مزودي خدمة الإيواء؟"}}}}], "properties": {"building_image": {"type": "string", "title": "صورة واجهة المبنى", "format": "uri"}, "external_units": {"type": "integer", "title": "عد<PERSON> الوحدات الخارجية"}, "building_floors": {"type": "integer", "title": "<PERSON><PERSON><PERSON> أدو<PERSON><PERSON> المبنى"}, "has_multiple_facades": {"type": "string", "title": "هل للمبنى أكثر من واجهة؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_commercial_units": {"type": "string", "title": "هل المبنى يحتوي وحدات تجارية؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_housing_providers": {"type": "string", "title": "هل المبنى يحتوي مزود خدمة إيواء؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["building_image", "external_units", "building_floors", "has_multiple_facades", "has_commercial_units", "has_housing_providers"]}, "electricity_details": {"title": "تفاصيل عدادات الكهرباء", "type": "object", "properties": {"electricity_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "uri"}, "electricity_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "uri"}}, "required": ["electricity_meter_image", "electricity_meter_info"]}, "water_details": {"title": "تفاصيل عدادات المياه", "type": "object", "properties": {"water_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "uri"}, "water_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "uri"}}, "required": ["water_meter_image", "water_meter_info"]}, "setback_details": {"title": "تفاصيل الإرتداد", "type": "object", "properties": {"setback_direction": {"type": "string", "title": "إتجاه الإرتداد"}, "is_accessible": {"type": "string", "title": "هل يمكن الدخول الى الإرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["setback_direction", "is_accessible"]}, "parking_details": {"title": "تفاصيل المواقف", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_length": {"type": "integer", "title": "طول المواقف"}, "parking_width": {"type": "integer", "title": "عرض المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}}, "properties": {"interface": {"title": "الواجهة", "type": "object", "properties": {"has_building": {"title": "هل يوجد مبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_building"], "allOf": [{"if": {"properties": {"has_building": {"const": "yes"}}}, "then": {"properties": {"building_details": {"$ref": "#/definitions/building_details"}}, "required": ["building_details"]}}]}, "infrastructure": {"title": "خدمات البنية التحتية", "type": "object", "properties": {"has_electricity_meter": {"title": "هل تتوفر عدادات الكهرباء؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_water_meter": {"title": "هل تتوفر عدادات المياه؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"has_electricity_meter": {"const": "yes"}}}, "then": {"properties": {"electricity_details": {"$ref": "#/definitions/electricity_details"}}, "required": ["electricity_details"]}}, {"if": {"properties": {"has_water_meter": {"const": "yes"}}}, "then": {"properties": {"water_details": {"$ref": "#/definitions/water_details"}}, "required": ["water_details"]}}]}, "setbacks": {"title": "الإرتداد", "type": "object", "properties": {"has_setback": {"title": "هل يوجد إرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_setback"], "allOf": [{"if": {"properties": {"has_setback": {"const": "yes"}}}, "then": {"properties": {"setback_directions_count": {"type": "integer", "title": "عدد الجها<PERSON> التي يوجد بها ارتداد جانبي؟", "default": 0}, "setback_details": {"type": "array", "title": "تفاصيل الإرتداد", "items": {"type": "object", "title": "", "if": {"properties": {"is_accessible": {"const": "yes"}}}, "then": {"properties": {"setback_length": {"type": "number", "title": "طول الإرتداد", "default": 0}, "setback_width": {"type": "number", "title": "عرض الإرتداد", "default": 0}, "setback_image": {"type": "string", "title": "صورة الإرتداد", "format": "uri"}}}, "properties": {"direction": {"type": "string", "title": "إتجاه الإرتداد", "$ref": "#/definitions/yes_no", "default": "no"}, "is_accessible": {"type": "string", "title": "هل يمكن الدخول الى الإرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["setback_directions_count", "direction", "is_accessible", "setback_length", "setback_width", "setback_image"]}}}, "required": ["setback_details"]}}]}, "parking": {"title": "المواقف", "type": "object", "properties": {"front_parking": {"title": "هل يوجد مواقف في واجهة المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}, "underground_parking": {"title": "هل يوجد مواقف في أسفل المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}, "rear_parking": {"title": "هل يوجد مواقف في خلف المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["front_parking", "underground_parking", "rear_parking"], "allOf": [{"if": {"properties": {"front_parking": {"const": "yes"}}}, "then": {"properties": {"front_parking_details": {"type": "object", "$ref": "#/definitions/front_parking_details"}}, "required": ["front_parking_details"]}}, {"if": {"properties": {"underground_parking": {"const": "yes"}}}, "then": {"properties": {"underground_parking_details": {"type": "object", "$ref": "#/definitions/underground_parking_details"}}, "required": ["underground_parking_details"]}}, {"if": {"properties": {"rear_parking": {"const": "yes"}}}, "then": {"properties": {"rear_parking_details": {"type": "object", "$ref": "#/definitions/rear_parking_details"}}, "required": ["rear_parking_details"]}}]}, "entrances": {"title": "المداخل", "type": "object", "properties": {"entrance_count": {"type": "integer", "title": "عدد مداخل المبنى؟"}, "entrance_images": {"type": "array", "title": "صور المداخل", "items": {"type": "string", "format": "uri"}}, "has_corridors": {"title": "هل يمكن الدخول الى ممرات المبنى الداخلية في الدور الأرضي؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_elevators": {"title": "هل يوجد مصاعد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["entrance_count", "entrance_images", "has_corridors", "has_elevators"], "allOf": [{"if": {"properties": {"has_corridors": {"const": "yes"}}}, "then": {"properties": {"corridor_details": {"title": "تفاصيل الممرات", "type": "array", "items": {"$ref": "#/definitions/corridor_details"}}}, "required": ["corridor_details"]}}, {"if": {"properties": {"has_elevators": {"const": "yes"}}}, "then": {"properties": {"elevator_details": {"title": "تفاصيل المصاعد", "type": "array", "items": {"$ref": "#/definitions/elevator_details"}}}, "required": ["elevator_details"]}}]}}, "required": ["interface", "infrastructure", "setbacks", "parking", "entrances"]}