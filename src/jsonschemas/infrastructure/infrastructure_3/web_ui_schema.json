{"ui:order": ["interface", "infrastructure", "setbacks", "parking", "entrances", "stairs", "roof", "floors"], "interface": {"ui:order": ["has_building", "building_details", "facades_count", "commercial_units_count", "housing_providers_count"], "has_building": {"ui:widget": "YesNoWidget"}, "building_details": {"ui:order": ["building_image", "external_units", "building_floors", "has_multiple_facades", "facades_count", "has_commercial_units", "commercial_units_count", "has_housing_providers", "housing_providers_count"], "building_image": {"ui:widget": "FileWidget"}, "has_multiple_facades": {"ui:widget": "YesNoWidget"}, "has_commercial_units": {"ui:widget": "YesNoWidget"}, "has_housing_providers": {"ui:widget": "YesNoWidget"}}}, "infrastructure": {"ui:order": ["has_electricity_meter", "electricity_details", "has_water_meter", "water_details"], "has_electricity_meter": {"ui:widget": "YesNoWidget"}, "electricity_details": {"ui:order": ["electricity_meter_image", "electricity_meter_info"], "electricity_meter_image": {"ui:widget": "FileWidget"}, "electricity_meter_info": {"ui:widget": "FileWidget"}}, "has_water_meter": {"ui:widget": "YesNoWidget"}, "water_details": {"ui:order": ["water_meter_image", "water_meter_info"], "water_meter_image": {"ui:widget": "FileWidget"}, "water_meter_info": {"ui:widget": "FileWidget"}}}, "setbacks": {"ui:order": ["has_setback", "setback_directions_count", "setback_details"], "has_setback": {"ui:widget": "YesNoWidget"}, "setback_details": {"ui:order": ["setback_direction", "is_accessible"], "ui:itemTitle": "ارتداد", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة ارتداد", "items": {"is_accessible": {"ui:widget": "YesNoWidget"}, "setback_image": {"ui:widget": "FileWidget"}}}}, "parking": {"ui:order": ["front_parking", "front_parking_details", "underground_parking", "underground_parking_details", "rear_parking", "rear_parking_details"], "front_parking": {"ui:widget": "YesNoWidget"}, "front_parking_details": {"ui:order": ["parking_count", "parking_images"], "parking_images": {"ui:widget": "AddPhotoToGallery"}}, "underground_parking": {"ui:widget": "YesNoWidget"}, "underground_parking_details": {"ui:order": ["parking_count", "parking_length", "parking_width", "parking_images"], "parking_images": {"ui:widget": "AddPhotoToGallery"}}, "rear_parking": {"ui:widget": "YesNoWidget"}, "rear_parking_details": {"ui:order": ["parking_count", "parking_images"], "parking_images": {"ui:widget": "AddPhotoToGallery"}}}, "entrances": {"ui:order": ["entrance_count", "entrance_images", "has_corridors", "corridor_details", "has_elevators", "elevator_details"], "entrance_count": {"ui:widget": "updown"}, "entrance_images": {"ui:widget": "AddPhotoToGallery"}, "has_corridors": {"ui:widget": "YesNoWidget"}, "corridor_details": {"ui:itemTitle": "م<PERSON><PERSON>", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة ممر", "items": {"ui:order": ["corridor_number", "corridor_length", "corridor_width", "corridor_images"], "corridor_images": {"ui:widget": "AddPhotoToGallery"}}}, "has_elevators": {"ui:widget": "YesNoWidget", "ui:title": "هل يوجد مصاعد؟"}, "elevator_details": {"ui:itemTitle": "مصعد", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة مصعد", "items": {"ui:order": ["elevator_number", "elevator_length", "elevator_width", "elevator_images"], "elevator_images": {"ui:widget": "AddPhotoToGallery"}}}}, "stairs": {"ui:order": ["has_emergency_exits", "emergency_exit_details", "has_emergency_stairs", "emergency_stair_details"], "has_emergency_exits": {"ui:widget": "YesNoWidget", "ui:title": "هل يوجد مخارج للطوارئ؟"}, "emergency_exit_details": {"ui:order": ["emergency_exit_count", "emergency_exit_images"], "emergency_exit_images": {"ui:widget": "AddPhotoToGallery"}}, "has_emergency_stairs": {"ui:widget": "YesNoWidget", "ui:title": "هل يوجد سلم للطوارئ (درج للطوارئ)؟"}, "emergency_stair_details": {"ui:order": ["emergency_stair_count", "emergency_stair_images"], "emergency_stair_images": {"ui:widget": "AddPhotoToGallery"}}}, "roof": {"ui:order": ["has_roof_access", "roof_access_details", "has_upper_annex", "upper_annex_details"], "has_roof_access": {"ui:widget": "YesNoWidget", "ui:title": "هل يمكن الوصول للسطح؟"}, "roof_access_details": {"ui:order": ["roof_length", "roof_width", "roof_images"], "roof_images": {"ui:widget": "AddPhotoToGallery"}}, "has_upper_annex": {"ui:widget": "YesNoWidget", "ui:title": "هل يوجد ملاحق علوية؟"}, "upper_annex_details": {"ui:order": ["upper_annex_count", "upper_annex_images"], "upper_annex_images": {"ui:widget": "AddPhotoToGallery"}}}, "floors": {"ui:order": ["has_multiple_floors", "floor_details"], "has_multiple_floors": {"ui:widget": "YesNoWidget", "ui:title": "هل للمبنى اكثر من دور؟"}, "floor_details": {"ui:itemTitle": "دور", "ui:itemCollapsible": true, "ui:addMoreItemsTitle": "إضافة دور", "items": {"ui:order": ["floor_number", "has_corridors", "corridor_details"], "has_corridors": {"ui:widget": "YesNoWidget", "ui:title": "هل يمكن الدخول الى ممرات الدور؟"}, "corridor_details": {"ui:order": ["unit_count", "corridor_images", "unit_images"], "corridor_images": {"ui:widget": "AddPhotoToGallery"}, "unit_images": {"ui:widget": "AddPhotoToGallery"}}}}}}