{"$schema": "http://json-schema.org/draft-07/schema#", "title": "النموذج الرابع", "type": "object", "definitions": {"yes_no": {"enum": ["yes", "no"], "enumNames": ["نعم", "لا"]}, "setback_direction_enum": {"enum": ["يمين", "يسار", "خلفي"]}, "front_parking_details": {"title": "تفاصيل المواقف في واجهة المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}, "underground_parking_details": {"title": "تفاصيل المواقف في أسفل المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_length": {"type": "number", "title": "طول المواقف"}, "parking_width": {"type": "number", "title": "عرض المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_length", "parking_width", "parking_images"]}, "rear_parking_details": {"title": "تفاصيل المواقف في خلف المبنى", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}, "building_details": {"title": "تفاصيل المبنى", "type": "object", "allOf": [{"if": {"properties": {"has_multiple_facades": {"const": "yes"}}}, "then": {"properties": {"facades_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الواجهات"}}}}, {"if": {"properties": {"has_commercial_units": {"const": "yes"}}}, "then": {"properties": {"commercial_units_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الواجهات"}}}}, {"if": {"properties": {"has_housing_providers": {"const": "yes"}}}, "then": {"properties": {"housing_providers_count": {"type": "integer", "title": "كم عدد مزودي خدمة الإيواء؟"}}}}], "properties": {"building_image": {"type": "string", "title": "صورة واجهة المبنى", "format": "uri"}, "external_units": {"type": "integer", "title": "عد<PERSON> الوحدات الخارجية"}, "building_floors": {"type": "integer", "title": "<PERSON><PERSON><PERSON> أدو<PERSON><PERSON> المبنى"}, "has_multiple_facades": {"type": "string", "title": "هل للمبنى أكثر من واجهة؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_commercial_units": {"type": "string", "title": "هل المبنى يحتوي وحدات تجارية؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_housing_providers": {"type": "string", "title": "هل المبنى يحتوي مزود خدمة إيواء؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["building_image", "external_units", "building_floors", "has_multiple_facades", "has_commercial_units", "has_housing_providers"]}, "electricity_details": {"title": "تفاصيل عدادات الكهرباء", "type": "object", "properties": {"electricity_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "uri"}, "electricity_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "uri"}}, "required": ["electricity_meter_image", "electricity_meter_info"]}, "water_details": {"title": "تفاصيل عدادات المياه", "type": "object", "properties": {"water_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "uri"}, "water_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "uri"}}, "required": ["water_meter_image", "water_meter_info"]}, "setback_details": {"title": "تفاصيل الإرتداد", "type": "object", "properties": {"setback_direction": {"type": "string", "title": "إتجاه الإرتداد", "$ref": "#/definitions/setback_direction_enum"}, "is_accessible": {"type": "string", "title": "هل يمكن الدخول الى الإرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["setback_direction", "is_accessible"]}, "parking_details": {"title": "تفاصيل المواقف", "type": "object", "properties": {"parking_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> المواقف"}, "parking_length": {"type": "integer", "title": "طول المواقف"}, "parking_width": {"type": "integer", "title": "عرض المواقف"}, "parking_images": {"type": "array", "title": "صور للمواقف", "items": {"type": "string", "format": "uri"}}}, "required": ["parking_count", "parking_images"]}, "emergency_exit_details": {"title": "تفاصيل مخارج الطوارئ", "type": "object", "properties": {"emergency_exit_count": {"type": "integer", "title": "عد<PERSON> مخارج الطوارئ"}, "emergency_exit_images": {"type": "array", "title": "صور مخارج الطوارئ", "items": {"type": "string", "format": "data-url"}}}, "required": ["emergency_exit_count", "emergency_exit_images"]}, "emergency_stair_details": {"title": "تفاصيل سلالم الطوارئ", "type": "object", "properties": {"emergency_stair_count": {"type": "integer", "title": "عدد سلالم الطوارئ"}, "emergency_stair_images": {"type": "array", "title": "صور سلالم الطوارئ", "items": {"type": "string", "format": "uri"}}}, "required": ["emergency_stair_count", "emergency_stair_images"]}, "floor_levels": {"enum": ["القبو الأول", "القبو الثاني", "القبو الثالث", "الدور الأرضي", "الميزانين الأول", "الدور الأول", "الميزانين الثاني", "الدور الثاني", "الدور الثالث", "الدور الرابع", "الدور الخامس", "الدور السادس", "الدور السابع", "الدور الثامن", "الدور التاسع", "الدور العاشر", "الدور الحادي عشر", "الدور الثاني عشر", "الدور الثالث عشر", "الدور الرابع عشر", "الدور الخامس عشر", "الدور السادس عشر", "الدور السابع عشر", "الدور الثامن عشر", "الدور التاسع عشر", "الدور العشرون", "الدور الحادي والعشرون", "الدور الثاني والعشرون", "الدور الثالث والعشرون", "الدور الرابع والعشرون", "الدور الخامس والعشرون", "الدور السادس والعشرون", "الدور السابع والعشرون", "الدور الثامن والعشرون", "الدور التاسع والعشرون", "الدور الثلاثون", "الدور الحادي والثلاثون", "الدور الثاني والثلاثون", "الدور الثالث والثلاثون", "الدور الرابع والثلاثون", "الدور الخامس والثلاثون", "الدور السادس والثلاثون", "الدور السابع والثلاثون", "الدور الثامن والثلاثون", "الدور التاسع والثلاثون", "الدور الأربعون", "الدور الحادي والأربعون", "الدور الثاني والأربعون", "الدور الثالث والأربعون", "الدور الرابع والأربعون", "الدور الخامس والأربعون", "الدور السادس والأربعون", "الدور السابع والأربعون", "الدور الثامن والأربعون", "الدور التاسع والأربعون", "الدور الخمسون", "الدور الحادي والخمسون", "الدور الثاني والخمسون", "الدور الثالث والخمسون", "الدور الرابع والخمسون", "الدور الخامس والخمسون", "الدور السادس والخمسون", "الدور السابع والخمسون", "الدور الثامن والخمسون", "الدور التاسع والخمسون", "الدور الستون", "الدور الحادي والستون", "الدور الثاني والستون", "الدور الثالث والستون", "الدور الرابع والستون", "الدور الخامس والستون", "الدور السادس والستون", "الدور السابع والستون", "الدور الثامن والستون", "الدور التاسع والستون", "الدور السبعون", "الدور الحادي والسبعون", "الدور الثاني والسبعون", "الدور الثالث والسبعون", "الدور الرابع والسبعون", "الدور الخامس والسبعون", "الدور السادس والسبعون", "الدور السابع والسبعون", "الدور الثامن والسبعون", "الدور التاسع والسبعون", "الدور الثمانون", "الدور الحادي والثمانون", "الدور الثاني والثمانون", "الدور الثالث والثمانون", "الدور الرابع والثمانون", "الدور الخامس والثمانون", "الدور السادس والثمانون", "الدور السابع والثمانون", "الدور الثامن والثمانون", "الدور التاسع والثمانون", "الدور التسعون", "الدور الحادي والتسعون", "الدور الثاني والتسعون", "الدور الثالث والتسعون", "الدور الرابع والتسعون", "الدور الخامس والتسعون", "الدور السادس والتسعون", "الدور السابع والتسعون", "الدور الثامن والتسعون", "الدور التاسع والتسعون", "الدور المائة", "الدور الحادي والمائة", "الدور الثاني والمائة", "الدور الثالث والمائة", "الدور الرابع والمائة", "الدور الخامس والمائة", "الدور السادس والمائة", "الدور السابع والمائة", "الدور الثامن والمائة", "الدور التاسع والمائة", "الدور العاشر والمائة", "الدور الحادي عشر والمائة", "الدور الثاني عشر والمائة", "الدور الثالث عشر والمائة", "الدور الرابع عشر والمائة", "الدور الخامس عشر والمائة", "الدور السادس عشر والمائة", "الدور السابع عشر والمائة", "الدور الثامن عشر والمائة", "الدور التاسع عشر والمائة", "الدور العشرون والمائة", "الدور الحادي والعشرون والمائة", "الدور الثاني والعشرون والمائة", "الدور الثالث والعشرون والمائة", "الدور الرابع والعشرون والمائة", "السطح الأول", "السطح الثاني"]}, "floor_details": {"title": "تفاصيل الأدوار", "type": "object", "allOf": [{"if": {"properties": {"has_corridors": {"const": "yes"}}}, "then": {"properties": {"corridor_details": {"type": "object", "title": "تفاصيل ممرات الدور", "properties": {"unit_count": {"type": "integer", "title": "عد<PERSON> الوحدات بالدور"}, "corridor_images": {"type": "array", "title": "صور لممرات الدور", "items": {"type": "string", "format": "data-url"}}, "unit_images": {"type": "array", "title": "صور لوحدات الدور", "items": {"type": "string", "format": "data-url"}}}, "required": ["unit_count", "corridor_images", "unit_images"]}}, "required": ["corridor_details"]}}], "properties": {"floor_number": {"type": "string", "title": "رقم الدور", "$ref": "#/definitions/floor_levels"}, "has_corridors": {"type": "string", "title": "هل يمكن الدخول الى ممرات الدور؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["floor_number", "has_corridors"]}}, "properties": {"interface": {"title": "الواجهة", "type": "object", "properties": {"has_building": {"title": "هل يوجد مبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_building"], "allOf": [{"if": {"properties": {"has_building": {"const": "yes"}}}, "then": {"properties": {"building_details": {"$ref": "#/definitions/building_details"}}, "required": ["building_details"]}}]}, "infrastructure": {"title": "خدمات البنية التحتية", "type": "object", "properties": {"has_electricity_meter": {"title": "هل تتوفر عدادات الكهرباء؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_water_meter": {"title": "هل تتوفر عدادات المياه؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"has_electricity_meter": {"const": "yes"}}}, "then": {"properties": {"electricity_details": {"$ref": "#/definitions/electricity_details"}}, "required": ["electricity_details"]}}, {"if": {"properties": {"has_water_meter": {"const": "yes"}}}, "then": {"properties": {"water_details": {"$ref": "#/definitions/water_details"}}, "required": ["water_details"]}}]}, "setbacks": {"title": "الإرتداد", "type": "object", "properties": {"has_setback": {"title": "هل يوجد إرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_setback"], "allOf": [{"if": {"properties": {"has_setback": {"const": "yes"}}}, "then": {"properties": {"setback_directions_count": {"type": "integer", "title": "عدد الجها<PERSON> التي يوجد بها ارتداد جانبي؟", "default": 0}, "setback_details": {"type": "array", "title": "تفاصيل الإرتداد", "items": {"type": "object", "title": "", "if": {"properties": {"is_accessible": {"const": "yes"}}}, "then": {"properties": {"setback_length": {"type": "number", "title": "طول الإرتداد", "default": 0}, "setback_width": {"type": "number", "title": "عرض الإرتداد", "default": 0}, "setback_image": {"type": "string", "title": "صورة الإرتداد", "format": "uri"}}}, "properties": {"direction": {"type": "string", "title": "إتجاه الإرتداد", "$ref": "#/definitions/setback_direction_enum"}, "is_accessible": {"type": "string", "title": "هل يمكن الدخول الى الإرتداد؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["setback_directions_count", "direction", "is_accessible", "setback_length", "setback_width", "setback_image"]}}}, "required": ["setback_details"]}}]}, "parking": {"title": "المواقف", "type": "object", "properties": {"front_parking": {"title": "هل يوجد مواقف في واجهة المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}, "underground_parking": {"title": "هل يوجد مواقف في أسفل المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}, "rear_parking": {"title": "هل يوجد مواقف في خلف المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["front_parking", "underground_parking", "rear_parking"], "allOf": [{"if": {"properties": {"front_parking": {"const": "yes"}}}, "then": {"properties": {"front_parking_details": {"type": "object", "$ref": "#/definitions/front_parking_details"}}, "required": ["front_parking_details"]}}, {"if": {"properties": {"underground_parking": {"const": "yes"}}}, "then": {"properties": {"underground_parking_details": {"type": "object", "$ref": "#/definitions/underground_parking_details"}}, "required": ["underground_parking_details"]}}, {"if": {"properties": {"rear_parking": {"const": "yes"}}}, "then": {"properties": {"rear_parking_details": {"type": "object", "$ref": "#/definitions/rear_parking_details"}}, "required": ["rear_parking_details"]}}]}, "stairs": {"title": "السلالم", "type": "object", "properties": {"has_emergency_exits": {"title": "هل يوجد مخارج للطوارئ؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_emergency_stairs": {"title": "هل يوجد سلم للطوارئ (درج للطوارئ)؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_emergency_exits", "has_emergency_stairs"], "allOf": [{"if": {"properties": {"has_emergency_exits": {"const": "yes"}}}, "then": {"properties": {"emergency_exit_details": {"type": "object", "$ref": "#/definitions/emergency_exit_details"}}, "required": ["emergency_exit_details"]}}, {"if": {"properties": {"has_emergency_stairs": {"const": "yes"}}}, "then": {"properties": {"emergency_stair_details": {"type": "object", "$ref": "#/definitions/emergency_stair_details"}}, "required": ["emergency_stair_details"]}}]}, "floors": {"title": "الأدوار", "type": "object", "properties": {"has_multiple_floors": {"title": "هل للمبنى اكثر من دور؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_multiple_floors"], "allOf": [{"if": {"properties": {"has_multiple_floors": {"const": "yes"}}}, "then": {"properties": {"floor_details": {"title": "تفاصيل الأدوار", "type": "array", "items": {"$ref": "#/definitions/floor_details"}}}, "required": ["floor_details"]}}]}}, "required": ["interface", "infrastructure", "setbacks", "parking", "entrances", "stairs", "roof", "floors"]}