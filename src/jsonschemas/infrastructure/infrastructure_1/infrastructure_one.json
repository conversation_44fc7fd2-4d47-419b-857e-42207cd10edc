{"$schema": "http://json-schema.org/draft-07/schema#", "title": "النموذج الأول", "type": "object", "definitions": {"yes_no": {"enum": ["yes", "no"], "enumNames": ["نعم", "لا"]}, "building_details": {"title": "تفاصيل المبنى", "type": "object", "properties": {"building_image": {"type": "string", "title": "صورة واجهة المبنى", "format": "data-url"}, "external_units": {"type": "integer", "title": "عد<PERSON> الوحدات الخارجية"}, "building_floors": {"type": "integer", "title": "<PERSON><PERSON><PERSON> أدو<PERSON><PERSON> المبنى"}, "has_multiple_facades": {"type": "string", "title": "هل للمبنى أكثر من واجهة؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["building_image", "external_units", "building_floors", "has_multiple_facades"]}, "electricity_details": {"title": "تفاصيل عدادات الكهرباء", "type": "object", "properties": {"electricity_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "data-url"}, "electricity_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "data-url"}}, "required": ["electricity_meter_image", "electricity_meter_info"]}, "water_details": {"title": "تفاصيل عدادات المياه", "type": "object", "properties": {"water_meter_image": {"type": "string", "title": "صورة لعداد الخدمة", "format": "data-url"}, "water_meter_info": {"type": "string", "title": "صورة للوحة معلومات العداد", "format": "data-url"}}, "required": ["water_meter_image", "water_meter_info"]}}, "properties": {"interface": {"title": "الواجهة", "type": "object", "properties": {"has_building": {"title": "هل يوجد مبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["has_building"], "allOf": [{"if": {"properties": {"has_building": {"const": "yes"}}}, "then": {"if": {"properties": {"building_details": {"properties": {"has_multiple_facades": {"const": "yes"}}}}}, "then": {"properties": {"facades_count": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الواجهات"}}}, "properties": {"building_details": {"$ref": "#/definitions/building_details"}}, "required": ["building_details"]}}]}, "infrastructure": {"title": "خدمات البنية التحتية", "type": "object", "properties": {"has_electricity_meter": {"title": "هل تتوفر عدادات الكهرباء؟", "$ref": "#/definitions/yes_no", "default": "no"}, "has_water_meter": {"title": "هل تتوفر عدادات المياه؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"has_electricity_meter": {"const": "yes"}}}, "then": {"properties": {"electricity_details": {"$ref": "#/definitions/electricity_details"}}, "required": ["electricity_details"]}}, {"if": {"properties": {"has_water_meter": {"const": "yes"}}}, "then": {"properties": {"water_details": {"$ref": "#/definitions/water_details"}}, "required": ["water_details"]}}]}}, "required": ["interface", "infrastructure"]}