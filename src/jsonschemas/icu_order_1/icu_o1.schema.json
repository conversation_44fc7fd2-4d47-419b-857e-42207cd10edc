{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"yes_no": {"type": "string", "enum": ["yes", "no"], "enumNames": ["نعم", "لا"], "default": "no"}, "exist_not_exits": {"enum": ["exist", "not_exist"], "enumNames": ["يو<PERSON>د", "لا يوجد"]}, "offer_type": {"type": "string", "title": "نوع العرض", "enum": ["إيج<PERSON>ر", "بيع", "إستثمار", "تقبيل"]}, "property_type": {"type": "string", "title": "نوع العقار", "enum": ["<PERSON><PERSON><PERSON>", "فيلا", "فيلا دوبلكس", "عمارة", "برج", "مزرعة", "استراحة", "مكتب", "محطة", "شقة", "دور", "مل<PERSON><PERSON>", "غرفة", "<PERSON><PERSON><PERSON>", "معر<PERSON>", "مستودع", "ورشة"]}, "representative_type": {"type": "string", "enum": ["مالك", "وكيل", "وسيط حصري", "وسيط"]}, "ownership_document_type": {"type": "string", "enum": ["صك إلكتروني", "صك عادي", "صك قديم", "صك غير مفرغ", "مبايعة", "حجة إستحكام", "قرار زراعي", "أ<PERSON><PERSON><PERSON>"]}, "offer_duration": {"type": "string", "enum": ["يوم / أيام", "شهر / أشهر", "مفتوح"]}, "land_nature": {"type": "string", "enum": ["جبلية", "سهل", "<PERSON>ير ذلك", "سهل وجبل"]}, "land_level": {"type": "string", "enum": ["مرتفع", "مستوي", "من<PERSON><PERSON>ض"]}, "fence_type": {"type": "string", "enum": ["شينكو", "قماش", "خشب", "بلك", "<PERSON>ير ذلك"]}, "plan_main_use": {"type": "string", "enum": ["سكني", "سكني تجاري", "تجاري رئيسي", "تجاري داخلي", "صناعي", "زراعي", "عشوائيات"]}, "plan_secondary_use": {"type": "string", "enum": ["سكني", "سكني تجاري", "تجاري رئيسي", "تجاري داخلي", "صناعي", "زراعي", "عشوائيات"]}, "realestate_status": {"type": "string", "enum": ["جديد", "شبه جديد", "قديم"]}, "finishing_type": {"type": "string", "enum": ["<PERSON>ظم", "رديء", "عادي", "ديلوكس", "سوبر ديلوكس", "فاخر"]}, "facades_finishing_type": {"type": "string", "enum": ["<PERSON>ظم", "لياسة", "دهان", "بروفايل", "كسر رخام", "<PERSON><PERSON><PERSON>", "رخام", "جرانيت", "سيراميك", "بريكاست", "كلادينق", "زجاج", "جار", "شينكو", "جي أر سي", "قرميد"]}, "broker_response": {"type": "string", "enum": ["متعاون", "متحفظ", "غير متعاون", "لم يرد"]}, "representative_classification": {"type": "string", "enum": ["افراد", "مشروعات", "مكاتب عقارية"]}, "monitoring_type": {"type": "string", "enum": ["عر<PERSON> عقاري", "مكتب عقاري"]}, "property_required_features": {"type": "string", "enum": ["بيت شعر", "م<PERSON><PERSON><PERSON> مشترك", "م<PERSON><PERSON><PERSON> خاص", "حديقة خاصة", "حديقة مشتركة", "ملا<PERSON><PERSON> أطفال", "مواق<PERSON> سيارات", "موا<PERSON><PERSON> قبو", "مو<PERSON><PERSON>", "مصعد", "صالة رياضية", "غرفة خادمة", "غرفة سائق", "مستودع", "خزان مستقل", "شرفة", "تكييف مركزي", "نظام المنزل الذكي", "الواح للطاقة الشمسية", "نظام شحن السيارات", "خزانات استلام الطرود", "مواقف للدراجات الهوائية", "احواش خاصة", "اطلالات", "سطح"]}, "project_gurantees": {"type": "string", "enum": ["تمديدات الكهرباء", "تمديدات السباكة", "العزل المائي", "العزل الحراري", "الإضاءة", "الهيكل الانشائي", "القواطع والافياش", "خزانات المياه", "المصعد", "معدات المسبح", "الأدوات الصحية", "الدهانات", "النوافذ", "الأبواب", "الواجهات", "تصدعات المبنى"]}, "facade_orientation": {"type": "string", "enum": ["شمالية", "شرقية", "غربية", "جنوبية", "شمالية شرقية", "شمالية غربية", "جنوبية شرقية", "جنوبية غربية"]}, "purchase_mechanism": {"type": "string", "enum": ["كاش", "تمويل", "كلاهما"]}, "property_age": {"type": "string", "enum": ["جديد", "اقل من سنة", "سنة", "سنتين", "ثلاث سنوات", "اربع سنوات", "خمس سنوات", "ست سنوات", "سبع سنوات", "ثمان سنوات", "تسع سنوات", "عشر سنوات", "اكثر من عشر سنوات"]}, "annex_status": {"type": "string", "enum": ["جديد", "شبه جديد", "قديم"]}, "price_unit": {"type": "string", "enum": ["اجمالي", "لكل م2"]}, "annex_usage": {"type": "string", "enum": ["غرفة خادمة", "غرفة سائق", "مستودع", "غرفة ضيوف", "مكتب ", "صالة رياضية", "استوديو ", "ورشة عمل", "غرفة لعب للأطفال", "جناح مستقل", "شقة مؤجرة", "مرآب سيارات", "مخزن", "مطب<PERSON> خارجي"]}, "edge_type": {"type": "string", "enum": ["حد<PERSON><PERSON> طبيعية", "مر<PERSON><PERSON> عام", "<PERSON><PERSON><PERSON><PERSON>", "شارع", "فناء داخلي", "م<PERSON><PERSON>", "قطعة أرض"]}, "floors": {"type": "string", "enum": ["القبو الأول", "القبو الثاني", "القبو الثالث", "الدور الأرضي", "الميزانين الأول", "الدور الأول", "الميزانين الثاني", "الدور الثاني", "الدور الثالث", "الدور الرابع", "الدور الخامس", "الدور السادس", "الدور السابع", "الدور الثامن", "الدور التاسع", "الدور العاشر", "الدور الحادي عشر", "الدور الثاني عشر", "الدور الثالث عشر", "الدور الرابع عشر", "الدور الخامس عشر", "الدور السادس عشر", "الدور السابع عشر", "الدور الثامن عشر", "الدور التاسع عشر", "الدور العشرون", "الدور الحادي والعشرون", "الدور الثاني والعشرون", "الدور الثالث والعشرون", "الدور الرابع والعشرون", "الدور الخامس والعشرون", "الدور السادس والعشرون", "الدور السابع والعشرون", "الدور الثامن والعشرون", "الدور التاسع والعشرون", "الدور الثلاثون", "الدور الحادي والثلاثون", "الدور الثاني والثلاثون", "الدور الثالث والثلاثون", "الدور الرابع والثلاثون", "الدور الخامس والثلاثون", "الدور السادس والثلاثون", "الدور السابع والثلاثون", "الدور الثامن والثلاثون", "الدور التاسع والثلاثون", "الدور الأربعون", "الدور الحادي والأربعون", "الدور الثاني والأربعون", "الدور الثالث والأربعون", "الدور الرابع والأربعون", "الدور الخامس والأربعون", "الدور السادس والأربعون", "الدور السابع والأربعون", "الدور الثامن والأربعون", "الدور التاسع والأربعون", "الدور الخمسون", "الدور الحادي والخمسون", "الدور الثاني والخمسون", "الدور الثالث والخمسون", "الدور الرابع والخمسون", "الدور الخامس والخمسون", "الدور السادس والخمسون", "الدور السابع والخمسون", "الدور الثامن والخمسون", "الدور التاسع والخمسون", "الدور الستون", "الدور الحادي والستون", "الدور الثاني والستون", "الدور الثالث والستون", "الدور الرابع والستون", "الدور الخامس والستون", "الدور السادس والستون", "الدور السابع والستون", "الدور الثامن والستون", "الدور التاسع والستون", "الدور السبعون", "الدور الحادي والسبعون", "الدور الثاني والسبعون", "الدور الثالث والسبعون", "الدور الرابع والسبعون", "الدور الخامس والسبعون", "الدور السادس والسبعون", "الدور السابع والسبعون", "الدور الثامن والسبعون", "الدور التاسع والسبعون", "الدور الثمانون", "الدور الحادي والثمانون", "الدور الثاني والثمانون", "الدور الثالث والثمانون", "الدور الرابع والثمانون", "الدور الخامس والثمانون", "الدور السادس والثمانون", "الدور السابع والثمانون", "الدور الثامن والثمانون", "الدور التاسع والثمانون", "الدور التسعون", "الدور الحادي والتسعون", "الدور الثاني والتسعون", "الدور الثالث والتسعون", "الدور الرابع والتسعون", "الدور الخامس والتسعون", "الدور السادس والتسعون", "الدور السابع والتسعون", "الدور الثامن والتسعون", "الدور التاسع والتسعون", "الدور المائة", "الدور الحادي والمائة", "الدور الثاني والمائة", "الدور الثالث والمائة", "الدور الرابع والمائة", "الدور الخامس والمائة", "الدور السادس والمائة", "الدور السابع والمائة", "الدور الثامن والمائة", "الدور التاسع والمائة", "الدور العاشر والمائة", "الدور الحادي عشر والمائة", "الدور الثاني عشر والمائة", "الدور الثالث عشر والمائة", "الدور الرابع عشر والمائة", "الدور الخامس عشر والمائة", "الدور السادس عشر والمائة", "الدور السابع عشر والمائة", "الدور الثامن عشر والمائة", "الدور التاسع عشر والمائة", "الدور العشرون والمائة", "الدور الحادي والعشرون والمائة", "الدور الثاني والعشرون والمائة", "الدور الثالث والعشرون والمائة", "الدور الرابع والعشرون والمائة", "السطح الأول", "السطح الثاني"]}, "rebounds": {"type": "string", "enum": ["يمين", "يسار", "خلفي"]}, "realestate_classification": {"type": "string", "enum": ["سكني", "تجاري", "سكني تجاري (مخت<PERSON>ط)", "إداري", "صناعي", "زراعي", "ترفيهي", "سياحي", "تعليمي", "صحي", "أراضي خام", "أراضي مطورة", "مر<PERSON><PERSON> عام", "أ<PERSON><PERSON><PERSON>"]}, "commercial_units_classification": {"type": "string", "enum": ["محلات تجارية", "مكاتب", "شقق سكنية", "وحدات فندقية", "محطة وقود", "أكشاك", "شاليهات"]}, "multiple_images_upload": {"type": "array", "items": {"type": "string"}}, "floors_data": {"type": "array", "items": {"title": "حقا<PERSON><PERSON> الطابق", "properties": {"floor_number": {"title": "رقم الطابق", "$ref": "#/definitions/floors"}, "floor_units_classification": {"title": "ماهو تصنيف الوحدات في الطابق؟", "$ref": "#/definitions/commercial_units_classification"}, "floor_units_count": {"type": "number", "title": "عد<PERSON> الوحدات بالطابق؟", "minimum": 1}, "floor_units_images": {"title": "صور وحدات الطابق", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}, "floor_corridors_images": {"title": "صور لممرات الطابق", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}}, "required": ["floor_number", "floor_units_classification", "floor_units_count", "floor_units_images", "floor_corridors_images"]}, "minItems": 1}, "parks": {"properties": {"parks_count": {"type": "number", "title": "<PERSON><PERSON><PERSON> المواقف", "minimum": 1}, "images": {"title": "صور المواقف", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}}, "required": ["images", "parks_count"]}, "electricity_meters_data": {"title": "", "properties": {"meters_count": {"type": "number", "title": "<PERSON><PERSON><PERSON> عدادات الكهرباء", "minimum": 1}, "service_meters_images": {"title": "صور لعدادات الخدمة", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}, "meter_info_panels_images": {"title": "صور للوحات معلومات العداد", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}}, "required": ["meters_count", "service_meters_images", "meter_info_panels_images"]}, "water_meters_data": {"title": "", "properties": {"meters_count": {"type": "number", "title": "<PERSON><PERSON><PERSON> عداد<PERSON><PERSON> المياه", "minimum": 1}, "service_meters_images": {"title": "صور لعدادات الخدمة", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}, "meter_info_panels_images": {"title": "صور للوحات معلومات العداد", "$ref": "#/definitions/multiple_images_upload", "minItems": 1}}, "required": ["meters_count", "service_meters_images", "meter_info_panels_images"]}, "building_exists_yes": {"type": "object", "properties": {"floor_facts": {"title": "حقائق مبنى العقار", "type": "object", "properties": {"floors_count": {"title": "ع<PERSON><PERSON> طوا<PERSON><PERSON> المبنى", "type": "number", "minimum": 1}, "floors_data": {"title": "حقا<PERSON><PERSON> الطوابق", "$ref": "#/definitions/floors_data"}}, "required": ["floors_count", "floors_data"]}, "parks_facts": {"type": "object", "title": "المواقف", "properties": {"front_parks_data": {"title": "", "properties": {"front_parks_data_exists": {"title": "هل يوجد مواقف في واجهة المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["front_parks_data_exists"], "allOf": [{"if": {"properties": {"front_parks_data_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/parks"}}]}, "back_parks_data": {"title": "", "properties": {"back_parks_data_exists": {"title": "هل يوجد مواقف في خلف المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["back_parks_data_exists"], "allOf": [{"if": {"properties": {"back_parks_data_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/parks"}}]}, "bottom_parks_data": {"title": "", "properties": {"bottom_parks_data_exists": {"title": "هل يوجد مواقف في أسفل المبنى؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["bottom_parks_data_exists"], "allOf": [{"if": {"properties": {"bottom_parks_data_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/parks"}}]}}, "required": ["front_parks_data", "back_parks_data", "bottom_parks_data"]}, "infrastructure_services": {"title": "خدمات البنية التحتية", "type": "object", "properties": {"electricity_meters_data": {"title": "", "properties": {"electricity_meters_data_exists": {"title": "هل تتوفر عدادات الكهرباء؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["electricity_meters_data_exists"], "allOf": [{"if": {"properties": {"electricity_meters_data_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/electricity_meters_data"}}]}, "water_meters_data": {"title": "", "properties": {"water_meters_data_exists": {"title": "هل تتوفر عدادات المياه؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "required": ["water_meters_data_exists"], "allOf": [{"if": {"properties": {"water_meters_data_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/water_meters_data"}}]}}, "required": ["electricity_meters_data", "water_meters_data"]}}, "required": ["floor_facts", "parks_facts", "infrastructure_services"]}, "land_realestate_facts": {"title": "", "type": "object", "properties": {"realestate_images": {"$ref": "#/definitions/multiple_images_upload", "title": "صورة للعقار", "minItems": 1}, "building_exists": {"$ref": "#/definitions/yes_no", "title": "هل يوجد مبنى؟", "default": "no"}}, "allOf": [{"if": {"properties": {"building_exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/building_exists_yes"}}], "required": ["building_exists", "realestate_images"]}, "multiple_lands_yes": {"type": "object", "properties": {"form_filled_before": {"title": "هل تم تعبئة بيانات العقار في نموذج آخر؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"form_filled_before": {"const": "yes"}}}, "then": {"properties": {"original_form_number": {"title": "ماهو رقم النموذج الأساس؟", "type": "number", "minimum": 1}}, "required": ["original_form_number"]}}, {"if": {"properties": {"form_filled_before": {"const": "no"}}}, "then": {"properties": {"land_realestate_facts": {"$ref": "#/definitions/land_realestate_facts"}}}}]}}, "title": "تفاصيل العقار", "properties": {"realestate_multiple_land": {"title": "هل العقار يقع على أكثر من أرض؟", "$ref": "#/definitions/yes_no", "default": "no"}, "field_employee_data": {"type": "object", "title": "حقائق الماسح الميداني", "properties": {"descriptive_proof": {"title": "الإثبات الوصفي", "type": "string"}, "extra_images": {"title": "صور إضافيه", "$ref": "#/definitions/multiple_images_upload"}}}}, "required": ["realestate_multiple_land"], "allOf": [{"if": {"properties": {"realestate_multiple_land": {"const": "yes"}}}, "then": {"$ref": "#/definitions/multiple_lands_yes"}}, {"if": {"properties": {"realestate_multiple_land": {"const": "no"}}}, "then": {"properties": {"land_realestate_facts": {"$ref": "#/definitions/land_realestate_facts"}}}}]}