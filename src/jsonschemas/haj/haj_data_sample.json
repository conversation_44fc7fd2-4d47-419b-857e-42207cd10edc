{"has_housing_provider": "yes", "service_provider_facts": {"has_sign": "no", "establishment_status": "working", "number_of_floors": 1, "number_of_rooms": 1, "number_of_beds": 2, "activity_type": "travel", "establishment_category": "travel", "tourism_license": {"exists": "no"}, "haj_housing_license": {"exists": "yes", "document_number": "213213", "document_image": "http://example.com"}, "baladi_license": {"exists": "yes", "document_number": "BL987654321", "document_image": "https://example.com/images/baladi_license.jpg"}, "has_commercial_registry": {"exists": "no"}, "civil_defense_permit": {"exists": "no"}, "construction_license": {"exists": "no"}, "tax_registration": {"exists": "no"}}, "service_provider_area": {"region": "العاصمة المقدسة", "city": "مكة المكرمة", "address_location": "شارع الملك عبد الله، بجوار المسجد الحرام"}, "service_provider_details": {"name_arabic": "شركة مكة للسياحة", "name_english": "Makkah Tourism Company", "owner_name": "<PERSON><PERSON><PERSON><PERSON> علي", "owner_id_number": "*************", "employee_count_saudi": "1", "employee_count_non_saudi": "1", "has_licenses": "yes"}, "service_provider_data": {"employee_name": "محم<PERSON> عبد الله", "job_title": "1", "mobile_number": "**********", "website_url": "https://www.makkah-tourism.com", "x_account": "makkah_tourism", "instagram_account": "makkah_tourism_official", "snapchat_account": "makkah_sightseeing", "facebook_account": "makkah.tourism"}, "service_provider_infrastructure": {"electricity_meter_number": "1233123"}}