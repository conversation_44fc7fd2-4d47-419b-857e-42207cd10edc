{"$schema": "http://json-schema.org/draft-07/schema#", "title": "نموذج المسح مباني إسكان الحجاج", "type": "object", "definitions": {"yes_no_enum": {"type": "string", "enum": ["yes", "no"], "enumNames": ["نعم", "لا"]}, "establishment_status_enum": {"type": "string", "enum": ["working", "not_working"], "enumNames": ["تعمل", "لا تعمل"]}, "license_document_fields": {"properties": {"document_number": {"title": "رقم الوثيقة", "type": "string"}, "document_image": {"title": "صورة الوثيقة", "type": "string", "format": "data-url"}}}, "establishment_licenses_details": {"properties": {"service_provider_facts": {"title": "حقائق مزود الخدمة (تراخيص)", "properties": {"tourism_license": {"title": "هل يوجد رخصة سياحة؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "haj_housing_license": {"title": "هل يوجد رخصة إسكان حجاج؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "municipal_license": {"title": "هل يوجد رخصة بلدي؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "commercial_register": {"title": "هل يوجد سجل تجاري؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "civil_defense_permit": {"title": "هل يوجد تصريح دفاع مدني؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "construction_license": {"title": "هل يوجد رخصة بناء؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}, "tax_registration_license": {"title": "هل يوجد رخصة تسجيل ضريبي؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/license_document_fields"}}]}}}}}, "working_establishment_details": {"properties": {"service_provider_details": {"title": "تفاصيل مزود الخدمة", "properties": {"name_arabic": {"title": "اسم المنشأة باللغة العربية", "type": "string"}, "name_english": {"title": "اسم المنشأة باللغة الإنجليزية", "type": "string"}, "owner_name": {"title": "اسم مالك المنشأة", "type": "string"}, "owner_id_number": {"title": "رقم هوية مالك المنشأة", "type": "string"}, "has_licenses": {"$ref": "#/definitions/yes_no_enum", "title": "هل يوجد تراخيص؟", "default": "no"}, "number_of_floors": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الطوابق"}, "number_of_rooms": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الغرف"}, "number_of_beds": {"type": "integer", "title": "ع<PERSON><PERSON> الأسرة"}, "electricity_meter_number": {"type": "string", "title": "رقم عداد الكهرباء"}, "saudi_employees_count": {"type": "integer", "title": "عدد الموظفين السعوديين"}, "foreign_employees_count": {"type": "integer", "title": "ع<PERSON><PERSON> الموظفين الأجانب"}, "employee_name": {"type": "string", "title": "اسم الموظف"}, "job_title": {"type": "string", "title": "المسمى الوظيفي"}, "mobile_number": {"type": "string", "title": "رقم الجوال"}, "owner_contact_number": {"type": "string", "title": "رقم تواصل مالك المنشأة"}}, "required": ["name_arabic", "name_english", "owner_name", "owner_id_number", "has_licenses", "number_of_floors", "number_of_rooms", "number_of_beds", "electricity_meter_number", "saudi_employees_count", "foreign_employees_count", "employee_name", "job_title", "mobile_number", "owner_contact_number"], "allOf": [{"if": {"properties": {"has_licenses": {"const": "yes"}}}, "then": {"$ref": "#/definitions/establishment_licenses_details"}}]}}}, "not_working_establishment_details": {"properties": {"not_working_reason": {"title": "لماذا لا تعمل المنشأة؟", "type": "string", "enum": ["closed_during_work", "maintenance", "tourism_ministry_closure", "government_closure"], "enumNames": ["مغلق اثناء وقت العمل", "اعمال صيانه", "مغلق من وزاره السياحه", "مغلق من جهه حكوميه"]}}}, "accommodation_provider_details": {"properties": {"has_sign": {"title": "ھل یوجد لوحة؟", "$ref": "#/definitions/yes_no_enum", "default": "no"}, "establishment_status": {"title": "حالة المنشأة", "$ref": "#/definitions/establishment_status_enum", "default": "not_working"}}, "allOf": [{"if": {"properties": {"establishment_status": {"const": "working"}}}, "then": {"$ref": "#/definitions/working_establishment_details"}}, {"if": {"properties": {"establishment_status": {"const": "not_working"}}}, "then": {"$ref": "#/definitions/not_working_establishment_details"}}]}}, "properties": {"accommodation_providers_exist": {"title": "ھل یوجد مزودي خدمة إیواء؟", "$ref": "#/definitions/yes_no_enum", "default": "no"}}, "allOf": [{"if": {"properties": {"accommodation_providers_exist": {"const": "yes"}}}, "then": {"$ref": "#/definitions/accommodation_provider_details"}}]}