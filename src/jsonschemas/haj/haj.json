{"$schema": "http://json-schema.org/draft-07/schema#", "title": "نموذج مباني اسكان الحجاج", "type": "object", "definitions": {"yes_no": {"enum": ["yes", "no"], "enumNames": ["نعم", "لا"]}, "working_or_not": {"enum": ["working", "not_working"], "enumNames": ["تعمل", "لا تعمل"]}, "service_provider_area": {"title": "منطقة مزود الخدمة", "properties": {"region": {"type": "string", "title": "المنطقة", "enum": ["العاصمة المقدسة", "أمانة منطقة المدينة المنورة"]}, "city": {"type": "string", "title": "المدينة", "enum": ["مكة المكرمة", "المدينة المنورة"]}, "address_location": {"type": "string", "title": "الموقع/العنوان الوطني"}}, "required": ["region", "city", "address_location"]}, "infrastructure_services": {"title": "", "properties": {"electricity_meter_number": {"type": "string", "title": "رقم عداد الكهرباء"}}, "required": ["electricity_meter_number"]}, "service_provider_facts_license": {"properties": {"document_number": {"title": "رقم الوثيقة", "type": "string"}, "document_image": {"title": "صورة الوثيقة", "type": "string", "format": "uri"}}, "required": ["document_number", "document_image"]}, "establishment_has_licenses_yes": {"properties": {"tourism_license": {"title": "هل يوجد رخصة سياحة؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "activity_type": {"title": "نوع نشاط المنشأة", "type": "string"}, "establishment_category": {"title": "فئة المنشأة", "type": "string"}, "haj_housing_license": {"title": "هل يوجد رخصة إسكان حجاج؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "baladi_license": {"title": "هل يوجد رخصة اسكان بلدي؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "has_commercial_registry": {"title": "هل يوجد سجل تجاري؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "civil_defense_permit": {"title": "هل يوجد تصريح دفاع مدني؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "construction_license": {"title": "هل يوجد رخصة بناء؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}, "tax_registration": {"title": "هل يوجد رخصة تسجيل ضريبي؟", "properties": {"exists": {"title": "يو<PERSON>د", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"exists": {"const": "yes"}}}, "then": {"$ref": "#/definitions/service_provider_facts_license"}}], "required": ["exists"]}}, "required": ["tourism_license", "activity_type", "establishment_category", "haj_housing_license", "baladi_license", "has_commercial_registry", "civil_defense_permit", "construction_license", "tax_registration"]}, "establishment_status_working": {"properties": {"service_provider_details": {"title": "تفاصيل مزود الخدمة", "properties": {"name_arabic": {"title": "اسم المنشأة باللغة العربية", "type": "string"}, "name_english": {"title": "اسم المنشأة باللغة الإنجليزية", "type": "string"}, "owner_name": {"title": "اسم مالك المنشأة", "type": "string"}, "owner_id_number": {"title": "رقم هوية مالك المنشأة", "type": "string"}, "employee_count_saudi": {"title": "عدد الموظفين السعوديين", "type": "number"}, "employee_count_non_saudi": {"title": "ع<PERSON><PERSON> الموظفين الأجانب", "type": "number"}, "has_licenses": {"$ref": "#/definitions/yes_no", "title": "هل يوجد تراخيص؟", "default": "no"}, "service_provider_facts": {"title": "", "properties": {"number_of_floors": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الطوابق"}, "number_of_rooms": {"type": "integer", "title": "<PERSON><PERSON><PERSON> الغرف"}, "number_of_beds": {"type": "integer", "title": "ع<PERSON><PERSON> الأسرة"}}, "required": ["number_of_floors", "number_of_rooms", "number_of_beds"]}, "service_provider_infrastructure": {"$ref": "#/definitions/infrastructure_services"}}, "required": ["name_arabic", "name_english", "owner_name", "owner_id_number", "employee_count_saudi", "employee_count_non_saudi", "has_licenses", "service_provider_infrastructure", "service_provider_facts"], "allOf": [{"if": {"properties": {"has_licenses": {"const": "yes"}}}, "then": {"$ref": "#/definitions/establishment_has_licenses_yes"}}]}, "service_provider_data": {"title": "بيانات مزود الخدمة", "properties": {"employee_name": {"title": "اسم الموظف", "type": "string"}, "job_title": {"title": "المسمى الوظيفي", "type": "string"}, "mobile_number": {"title": "رقم الجوال", "type": "string"}, "establishment_owner_mobile_number": {"title": "رقم تواصل مالك المنشأة", "type": "string"}}, "required": ["employee_name", "job_title", "mobile_number", "establishment_owner_mobile_number"]}}, "required": ["service_provider_details", "service_provider_data"]}, "establishment_status_not_working": {"properties": {"service_provider_facts": {"title": "", "properties": {"not_working_reason": {"title": "لماذا لا تعمل المنشأة؟", "type": "string", "enum": ["close_during_working_hours", "maintenance", "tourism_ministry_closed", "government_closed"], "enumNames": ["مغلق أثناء أوقات العمل", "أعمال صيانة", "مغلق من وزارة السياحة", "مغلق من جهة حكومية"]}}, "required": ["not_working_reason"]}}, "required": ["service_provider_facts"]}, "has_housing_provider_yes": {"properties": {"service_provider_facts": {"title": "", "properties": {"has_sign": {"title": "ھل یوجد لوحة؟", "$ref": "#/definitions/yes_no", "default": "no"}, "establishment_status": {"title": "حالة المنشأة", "$ref": "#/definitions/working_or_not", "default": "not_working"}}, "required": ["has_sign", "establishment_status"]}}, "allOf": [{"if": {"properties": {"service_provider_facts": {"properties": {"has_sign": {"const": "yes"}}}}}, "then": {"properties": {"service_provider_facts": {"properties": {"sign_image_urls": {"type": "array", "items": {"type": "string", "format": "uri"}, "title": "صور اللوحات", "minItems": 1}}, "required": ["sign_image_urls"]}}}}, {"if": {"properties": {"service_provider_facts": {"properties": {"establishment_status": {"const": "working"}}}}}, "then": {"$ref": "#/definitions/establishment_status_working"}}, {"if": {"properties": {"service_provider_facts": {"properties": {"establishment_status": {"const": "not_working"}}}}}, "then": {"$ref": "#/definitions/establishment_status_not_working"}}]}}, "properties": {"has_housing_provider": {"title": "هل يوجد مزودي خدمة إيواء؟", "$ref": "#/definitions/yes_no", "default": "no"}}, "allOf": [{"if": {"properties": {"has_housing_provider": {"const": "yes"}}}, "then": {"$ref": "#/definitions/has_housing_provider_yes"}}], "required": ["has_housing_provider"]}