{"has_travel_office": "yes", "service_provider_facts": {"has_sign": "yes", "establishment_status": "working", "sign_image_url": "<PERSON><PERSON>", "activity_type": "234", "establishment_category": "4234", "tourism_license": {"exists": "yes", "document_number": "TS123456789", "document_image": "https://example.com/images/tourism_license.jpg"}, "baladi_license": {"exists": "yes", "document_number": "BL987654321", "document_image": "https://example.com/images/baladi_license.jpg"}, "has_commercial_registry": {"exists": "no"}, "has_unified_establishment_number": {"exists": "no"}, "tax_registration": {"exists": "no"}}, "service_provider_area": {"region": "العاصمة المقدسة", "city": "مكة المكرمة", "address_location": "شارع الملك عبد الله، بجوار المسجد الحرام"}, "service_provider_details": {"name_arabic": "شركة مكة للسياحة", "name_english": "Makkah Tourism Company", "owner_name": "<PERSON><PERSON><PERSON><PERSON> علي", "owner_id_number": "*************", "employee_count_saudi": "25", "employee_count_non_saudi": "15", "has_licenses": "yes"}, "service_provider_data": {"employee_name": "محم<PERSON> عبد الله", "job_title": "مدير فرع مكة", "mobile_number": "**********", "website_url": "https://www.makkah-tourism.com", "x_account": "makkah_tourism", "instagram_account": "makkah_tourism_official", "snapchat_account": "makkah_sightseeing", "facebook_account": "makkah.tourism"}}