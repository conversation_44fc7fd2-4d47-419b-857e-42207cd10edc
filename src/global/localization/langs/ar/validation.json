{"default": "{{field}} غير صالح", "required": "{{field}} مطلوب", "oneOf": "{{field}} يج<PERSON> أن يكون إحدى القيم التالية: ${values}", "notOneOf": "{{field}} يج<PERSON> <PERSON><PERSON><PERSON> يكون إحدى القيم التالية: ${values}", "defined": "{{field}} يج<PERSON> تحديده", "string": {"length": "{{field}} يج<PERSON> الا يزيد عن ${length} حروف", "min": "{{field}} يج<PERSON> ان يحتوي على ${min} حروف على الأقل", "max": "{{field}} يج<PERSON> ان يحتوي على ${max} حروف على الأكثر", "matches": "{{field}} غي<PERSON> صحيح", "email": "{{field}} يج<PERSON> ان يكون بريدا اليكتروني صحيح", "url": "{{field}} يج<PERSON> ان يكون عنوان صحيح", "uuid": "{{field}} يجب ان يكون رقماً تعريفياً غير متكرر", "trim": "{{field}} يجب أن لا يحاتوي على أكثر من مسافية بين الكلمات", "lowercase": "{{field}} يجب أن يكون أحرف صغيرة", "uppercase": "{{field}} يج<PERSON> أن يكون أحرف كبيرة"}, "number": {"min": "قيمة {{field}} يجب ان تكون أكبر من ${min} او تساويها", "max": "قيمة {{field}} يجب ان تكون اصغر من ${max} او تساويها", "lessThan": "قيمة {{field}} يجب ان تكون أقل من ${less}", "moreThan": "قيمة {{field}} يجب ان تكون أكبر من ${more}", "positive": "{{field}} يجب ان يكون رقماً موجاباً", "negative": "{{field}} يجب ان يكون رقماً سالباً", "integer": "{{field}} يجب ان يكون رقماً صحيحاً", "digits": "{{field}} يج<PERSON> أن يكون أرقام فقط", "digits_len": "{{field}} يجب أن يتكون من {{digits}} أرقام بالضبط"}, "date": {"min": "{{field}} يجب أن يكون بعد ${min}", "max": "{{field}} يجب أن يكون قبل ${max}"}, "boolean": {"isValue": "{{field}} يجب أن يكون ${value}"}, "object": {"noUnknown": "{{field}} يجتوي على قيم غير محددة: ${unknown}"}, "array": {"min": "{{field}} يجب أن يحتوي علي الأقل ${min} عناصر", "max": "{{field}} يجب أن يحتوي علي اﻷكثر أو يساوي ${max} عناصر", "length": "{{field}} يجب أن يحتوب علي ${length} عناصر"}}