{"default": "{{field}} is invalid", "required": "{{field}} is a required field", "oneOf": "{{field}} must be one of the following values: ${values}", "notOneOf": "{{field}} must not be one of the following values: ${values}", "defined": "{{field}} must be defined", "string": {"length": "{{field}} must be exactly ${length} characters", "min": "{{field}} must be at least ${min} characters", "max": "{{field}} must be at most ${max} characters", "matches": "{{field}} must match the following: \"${regex}\"", "email": "{{field}} must be a valid email", "url": "{{field}} must be a valid URL", "uuid": "{{field}} must be a valid UUID", "trim": "{{field}} must be a trimmed string", "lowercase": "{{field}} must be a lowercase string", "uppercase": "{{field}} must be a upper case string"}, "number": {"min": "{{field}} must be greater than or equal to ${min}", "max": "{{field}} must be less than or equal to ${max}", "lessThan": "{{field}} must be less than ${less}", "moreThan": "{{field}} must be greater than ${more}", "positive": "{{field}} must be a positive number", "negative": "{{field}} must be a negative number", "integer": "{{field}} must be an integer", "digits": "{{field}} must be only digits", "digits_len": "{{field}} must be exactly {{digits}} digits"}, "date": {"min": "{{field}} field must be later than ${min}", "max": "{{field}} field must be at earlier than ${max}"}, "boolean": {"isValue": "{{field}} field must be ${value}"}, "object": {"noUnknown": "{{field}} field has unspecified keys: ${unknown}"}, "array": {"min": "{{field}} field must have at least ${min} items", "max": "{{field}} field must have less than or equal to ${max} items", "length": "{{field}} must be have ${length} items"}}