export interface TaskI {
  id: string
  name: string
}

export type ProjectI = 'vd' | 'poi' | 'offers' | 'simple-project'

export interface ITaskState {
  taskList: TaskI[]
  selectedTask: TaskI
  selectedFeature: any
  selectedStatusFilter: string
  selectedCode: string
  searchQuery: string
  offsetPage: number
  tasksCount: number
  selectedRecordSchema: { jsonSchema: any; webUiJsonSchema: any }
}
