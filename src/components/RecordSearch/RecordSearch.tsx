import React, { useState } from 'react'
import IconButton from '@material-ui/core/IconButton'
import SearchIcon from '@material-ui/icons/Search'
import { TextField } from '@material-ui/core'
import AutorenewIcon from '@material-ui/icons/Autorenew'
import { useSelector, useDispatch } from 'react-redux'
import { RasedMap } from 'pages/Map/RasedMap'
import { RootState } from 'store'
import { gqlClient } from 'lib/graphql'
import { SEARCH_SIMPLE_RECORDS } from 'global'
import { logError, getSearchFieldOptions } from 'utils'
import { updateSelectedRecord } from 'store/layers'
import { toggleFormDrawer } from 'store/layout'
import { updateSelectedFeature } from 'store/tasks'
import toast from 'react-hot-toast'
import { useTranslation } from '@flint/locales'
import GeoJSON from 'ol/format/GeoJSON'
import { useStyle } from './RecordSearch.style'

export const getPolygonExtent = (geojsonObject: any) => {
  const geoJsonFeature = new GeoJSON().readFeature(geojsonObject, {
    dataProjection: 'EPSG:4326',
    featureProjection: 'EPSG:3857',
  }) as any
  const extent = geoJsonFeature.getGeometry().getExtent()
  console.log({ extent })

  return extent
}

interface Props {
  mapRef?: React.MutableRefObject<RasedMap>
}

export const RecordSearch = ({ mapRef }: Props) => {
  const classes = useStyle()
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const { selectedListItem } = useSelector((state: RootState) => state.layout)
  const { simpleProjectJsonSchema } = useSelector(
    (state: RootState) => state.layers
  )

  const configuration = simpleProjectJsonSchema?.configuration
  const searchFieldOptions = getSearchFieldOptions(configuration)

  const handleSearch = async () => {
    if (!selectedListItem?.id) {
      toast.error(t('no-selected-task'), {
        duration: 2000,
      })
      return
    }
    if (!searchValue) {
      toast.error(t('please-enter-search-value'), {
        duration: 2000,
      })
      return
    }

    // Ensure we have a search field configured
    if (!searchFieldOptions.length) {
      toast.error(t('no-search-fields-configured'), {
        duration: 2000,
      })
      return
    }

    try {
      // Use only the first search field from configuration
      const searchField = searchFieldOptions[0]
      const filter = {
        field: searchField.value,
        value: searchValue,
      }

      const result = await gqlClient.query({
        query: SEARCH_SIMPLE_RECORDS,
        variables: {
          taskId: selectedListItem?.id,
          filters: [filter],
          pageInfo: { limit: 1, offset: 0 },
        },
        fetchPolicy: 'network-only', // Prevent duplicate requests
      })

      const record = result?.data?.records?.data?.[0]

      if (record) {
        console.log(
          'first coord:',
          record.geometry.geometries[0].coordinates[0][0]
        )
        // Use sourceProperties for the actual data
        const recordData = {
          ...(record?.sourceProperties || {}),
        }

        const selectedRecord = {
          id: record.id,
          data: recordData,
          sourceProperties: record?.sourceProperties || {},
        }

        // Update selected record in store
        dispatch(updateSelectedRecord(selectedRecord))

        // Open the form drawer
        dispatch(toggleFormDrawer(true))

        // Zoom to and highlight the record on map
        if (mapRef?.current && record.geometry) {
          try {
            // 1. Create a proper GeoJSON feature from the record geometry (EPSG:4326)
            const originalFeature = {
              type: 'Feature' as const,
              id: `demo_order_1_data_layer.${record.id}`,
              geometry: record.geometry, // This is in EPSG:4326
              geometry_name: 'geometry',
              properties: {
                id: parseInt(record.id, 10),
                map_data: JSON.stringify(record.mapData || {}),
                layer_id: 1,
              },
            }

            // 2. Get the extent for zooming (transforms from EPSG:4326 to EPSG:3857 internally)
            const extent = getPolygonExtent(originalFeature)
            const view = mapRef.current.getView()
            view.fit(extent, {
              duration: 2000,
              padding: [150, 150, 150, 150],
            })

            // 3. Transform the feature to EPSG:3857 for highlighting
            // The highlighting system expects features in EPSG:3857 format
            const olFeature = new GeoJSON().readFeature(originalFeature, {
              dataProjection: 'EPSG:4326',
              featureProjection: 'EPSG:3857',
            })

            // Convert back to GeoJSON object in EPSG:3857 for the highlighting system
            const transformedFeature = new GeoJSON().writeFeatureObject(
              olFeature
            )

            // Clear any existing highlights first
            mapRef.current.clearHighlightedFeature()

            // Update the selected feature in the store with the transformed feature
            dispatch(updateSelectedFeature(transformedFeature))

            // Let the map handle the feature highlighting
            mapRef.current.handleSelectedFeature(transformedFeature)
          } catch (geometryError) {
            logError('MapError', geometryError, [
              'record search',
              'geometry processing',
            ])
            toast.error(t('error-processing-geometry'), {
              duration: 2000,
            })
          }
        }

        // Close search
        setIsSearchOpen(false)
        setSearchValue('')
      } else {
        toast.error(t('no-record-found'), {
          duration: 2000,
        })
      }
    } catch (error) {
      logError('NetworkError', error, ['simple project', 'searching record'])
    }
  }

  return (
    <div className={classes.root}>
      <IconButton
        className={classes.searchIconBtn}
        onClick={() => setIsSearchOpen(!isSearchOpen)}
      >
        <SearchIcon color="primary" />
      </IconButton>

      {isSearchOpen && (
        <>
          <TextField
            className={classes.searchInput}
            id="search-task"
            variant="outlined"
            placeholder="البحث عن سجل"
            size="small"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />

          <IconButton className={classes.sendIconBtn} onClick={handleSearch}>
            <AutorenewIcon
              color="primary"
              style={{ transform: 'rotate(180deg)' }}
            />
          </IconButton>
        </>
      )}
    </div>
  )
}
