import { useTranslation } from '@flint/locales'
import { ReactJsonSchemaForm } from '@flint/rjsf'
import {
  Button,
  Divider,
  FormControl,
  Select,
  MenuItem,
} from '@material-ui/core'
import validator from '@rjsf/validator-ajv8'
import React, { useState } from 'react'
import toast from 'react-hot-toast'
import { AddPhotoToGallery } from 'Rjsf'
import { prettyLogFromErrors } from 'utils/general.utils'
import { useNavigate, useLocation } from 'react-router-dom'
import { useStyle } from './FormPreview.style'

const defaultFormStateBehavior: any = { allOf: 'populateDefaults' }
const transformErrors = (errors: any) => {
  const modfiedErrors = errors?.map((err: any) => {
    if (err.name === 'required' || err.name === 'minItems') {
      return { ...err, message: 'حقل مطلوب' }
    }
    if (err.name === 'enum') {
      return { ...err, message: 'يرجى الإختيار من القيم الموجودة' }
    }
    return err
  })
  return modfiedErrors
}

export function FormPreview({ schema, uiSchema }: any) {
  const { t } = useTranslation()
  const classes = useStyle()
  const navigate = useNavigate()
  const location = useLocation()
  const [formData, setFormData] = useState(null)

  const schemaRoutes = [
    { path: '/uo-plan-4-schema/1', label: 'النموذج الأول' },
    { path: '/uo-plan-4-schema/2', label: 'النموذج الثاني' },
    { path: '/uo-plan-4-schema/3', label: 'النموذج الثالث' },
    { path: '/uo-plan-4-schema/4', label: 'النموذج الرابع' },
    { path: '/uo-plan-4-schema/4', label: 'النموذج الرابع' },
    { path: '/icu-order-1/1', label: 'الرقابة على الوحدات المخالفة' },
  ]

  const handleSchemaChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    navigate(event.target.value as string)
  }

  const onFileChange = () => {
    return ['https://example.com']
  }

  const onSubmit = () => {
    // eslint-disable-next-line
    console.log({ formData })
    toast.success(t('form-submitted-success'), {
      duration: 1500,
    })
  }
  const onError = (errors: any) => {
    prettyLogFromErrors(errors)
    toast.error(t('form_error_msg'), {
      duration: 1500,
    })
  }
  return (
    <div className={classes.container}>
      <FormControl
        variant="outlined"
        size="small"
        className={classes.schemaSelect}
      >
        <Select value={location.pathname} onChange={handleSchemaChange}>
          {schemaRoutes.map((route) => (
            <MenuItem key={route.path} value={route.path}>
              {route.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <div className={classes.formPreviewWrapper}>
        <ReactJsonSchemaForm
          validator={validator}
          experimental_defaultFormStateBehavior={defaultFormStateBehavior}
          transformErrors={transformErrors}
          onSubmit={onSubmit}
          onError={onError}
          schema={schema as any}
          uiSchema={uiSchema as any}
          formData={formData}
          formContext={{ onFileChange }}
          setFormData={setFormData}
          liveValidate={false}
          showErrorList={false}
          liveOmit
          omitExtraData
          noHtml5Validate
          widgets={{ AddPhotoToGallery }}
        >
          <Divider />
          <div className={classes.actionsContainer}>
            <Button
              className={classes.actionButton}
              variant="contained"
              color="primary"
              size="small"
              fullWidth
              disableElevation
              type="submit"
            >
              {t('save')}
            </Button>
            <Button
              className={classes.actionButton}
              variant="contained"
              color="default"
              size="small"
              type="reset"
              fullWidth
              disableElevation
            >
              {t('cancel')}
            </Button>
          </div>
        </ReactJsonSchemaForm>
      </div>
    </div>
  )
}
