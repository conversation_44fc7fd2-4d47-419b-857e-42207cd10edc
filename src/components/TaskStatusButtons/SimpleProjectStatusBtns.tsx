import { useState } from 'react'
import Button from '@material-ui/core/Button'
import { useDispatch } from 'react-redux'
import { takeTaskAction } from 'store/tasks/tasks.async'
import { takePoiTaskAction } from 'store/tasks/poi.async'
import { getSelectedProject } from 'utils'
import { useUserRole } from 'hooks/useUserRole'

import { ConfirmModal } from 'components/ConfirmModal'
import { useStyle } from './TaskStatusButtons.style'

const selectedProject = getSelectedProject()

export const TaskStatusButtons = ({ task }) => {
  const classes = useStyle()
  const dispatch = useDispatch()
  const { isSuperUser } = useUserRole()

  const [openModal, setOpenModal] = useState(false)
  const [actionType, setActionType] = useState(null)
  const [modalContent, setModalContent] = useState('')

  const handleCloseTask = (id) => {
    dispatch(
      selectedProject === 'offers'
        ? takeTaskAction({ actionType: 'close', id })
        : takePoiTaskAction({ actionType: 'close', id })
    )
  }
  const handleRejectTask = (id) => {
    dispatch(
      selectedProject === 'offers'
        ? takeTaskAction({ actionType: 'reject', id })
        : takePoiTaskAction({ actionType: 'reject', id })
    )
  }

  const handleConfirm = () => {
    setOpenModal(false)
    // Execute the action based on the selected actionType
    if (actionType === 'close') {
      handleCloseTask(task.id)
    } else if (actionType === 'reject') {
      handleRejectTask(task.id)
    }
  }

  const openModalWithContent = (type) => {
    setActionType(type)
    // Set modal content based on actionType
    if (type === 'close') {
      setModalContent('Are you sure you want to close this task?')
    } else if (type === 'reject') {
      setModalContent('Are you sure you want to reject this task?')
    }
    setOpenModal(true)
  }
  return (
    <>
      {((task?.status?.hasOwnProperty('assigned') && !isSuperUser) ||
        (!task?.status?.hasOwnProperty('assigned') && isSuperUser)) && (
        <Button
          className={classes.endBtn}
          onClick={() => openModalWithContent('close')}
        >
          إنهاء المهمة
        </Button>
      )}

      {isSuperUser && !task?.status?.hasOwnProperty('assigned') && (
        <Button
          className={classes.cancelBtn}
          onClick={() => openModalWithContent('reject')}
        >
          رفض
        </Button>
      )}
      <ConfirmModal
        open={openModal}
        onClose={() => setOpenModal(false)}
        onConfirm={handleConfirm}
        content={modalContent}
      />
    </>
  )
}
