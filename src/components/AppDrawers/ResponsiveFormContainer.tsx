import React, { useState } from 'react'
import { useTheme, useMediaQuery, Box, IconButton } from '@material-ui/core'
import { Close, Remove, CropFree, ExpandLess } from '@material-ui/icons'
import { FormDrawer } from './FormDrawer/FormDrawer'

interface ResponsiveFormContainerProps {
  form: React.ReactNode
  open?: boolean
  onClose?: () => void
}

export const ResponsiveFormContainer: React.FC<
  ResponsiveFormContainerProps
> = ({ form, open = false, onClose }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [isMinimized, setIsMinimized] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)

  if (isMobile) {
    // Use drawer on mobile
    return <FormDrawer form={form} />
  }

  // Use floating box on desktop
  if (!open) return null

  const getBoxStyle = () => {
    if (isMaximized) {
      return {
        position: 'fixed' as const,
        top: 20,
        left: 20,
        right: 20,
        bottom: 20,
        width: 'auto',
        maxWidth: 'none',
        maxHeight: 'none',
      }
    }

    if (isMinimized) {
      return {
        position: 'fixed' as const,
        bottom: 20,
        left: 20,
        width: 300,
        height: 40,
        maxWidth: 'calc(100vw - 40px)',
        maxHeight: 40,
      }
    }

    return {
      position: 'fixed' as const,
      bottom: 20,
      left: 20,
      width: 600,
      maxWidth: 'calc(100vw - 40px)',
      maxHeight: 'calc(100vh - 100px)',
    }
  }

  return (
    <Box
      style={{
        ...getBoxStyle(),
        backgroundColor: 'white',
        borderRadius: theme.spacing(1),
        boxShadow: theme.shadows[8],
        zIndex: 1300,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <Box
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start',
          padding: theme.spacing(0.5, 0.5),
          borderBottom: isMinimized
            ? 'none'
            : `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.grey[50],
          minHeight: 40,
        }}
      >
        <IconButton
          size="small"
          onClick={onClose}
          title="Close"
          style={{ padding: 6 }}
        >
          <Close />
        </IconButton>
        <IconButton
          size="small"
          onClick={() => setIsMaximized(!isMaximized)}
          title={isMaximized ? 'Restore' : 'Maximize'}
          style={{ padding: 6 }}
        >
          <CropFree />
        </IconButton>
        <IconButton
          size="small"
          onClick={() => setIsMinimized(!isMinimized)}
          title={isMinimized ? 'Expand' : 'Minimize'}
          style={{ padding: 6 }}
        >
          {isMinimized ? <ExpandLess /> : <Remove />}
        </IconButton>
      </Box>

      {/* Content */}
      {!isMinimized && (
        <Box
          style={{
            flex: 1,
            overflow: 'auto',
            padding: theme.spacing(2),
          }}
        >
          {form}
        </Box>
      )}
    </Box>
  )
}
