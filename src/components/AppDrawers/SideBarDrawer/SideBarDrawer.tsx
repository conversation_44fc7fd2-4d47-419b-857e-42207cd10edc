import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { IconButton } from '@material-ui/core'
import { Drawer } from 'components/Drawer'
import { CloseIcon } from 'icons'
import { RootState } from 'store'
import { toggleLayersDrawerOpen } from 'store/layout'

export const SideBarDrawer = ({ children }) => {
  const dispatch = useDispatch()
  const { layersDrawerOpen } = useSelector((state: RootState) => state.layout)

  return (
    <Drawer
      anchor="left"
      onClose={() => {
        dispatch(toggleLayersDrawerOpen(false))
      }}
      open={!!layersDrawerOpen}
      paperStyles={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        background: '#F1F3F2',
      }}
    >
      <IconButton
        onClick={() => dispatch(toggleLayersDrawerOpen(false))}
        style={{ alignSelf: 'flex-end', width: 'fit-content' }}
      >
        <CloseIcon />
      </IconButton>
      {children}
    </Drawer>
  )
}
