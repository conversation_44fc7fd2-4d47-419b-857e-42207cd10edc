import { SwapIcon } from 'icons'
// @ts-nocheck
import React from 'react'

import { useTranslation } from '@flint/locales'
import { IconButton, Typography } from '@material-ui/core'

import { useStyle } from './OrganizationSwitcher.style'

export function OrganizationSwitcher() {
  const classes = useStyle()
  const { t } = useTranslation()
  const activeOrg: any = {}
  return (
    <div className={classes.root}>
      <div className={classes.imgWrapper}>
        {activeOrg.fullLogo && (
          <img
            className={classes.logo}
            src={activeOrg.fullLogo}
            alt={`شعار ${activeOrg.name}`}
          />
        )}
        {!activeOrg.fullLogo && (
          <Typography className={classes.clientName}>
            {t('visual-distortion-project')}
          </Typography>
        )}
        <IconButton className={classes.swapButton}>
          <SwapIcon />
        </IconButton>
      </div>
    </div>
  )
}
