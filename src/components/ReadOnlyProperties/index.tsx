import React from 'react'
import { Typography, Paper, Divider } from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import jsonpath from 'jsonpath'

interface ReadOnlyPropertyConfig {
  root: string
  title: string
  jsonpath: string
}

interface ReadOnlyPropertiesProps {
  configuration: {
    readOnly?: ReadOnlyPropertyConfig[]
  }
  selectedRecord: {
    sourceProperties?: Record<string, any>
    data?: Record<string, any>
  }
}

const useStyles = makeStyles((theme) => ({
  readOnlyContainer: {
    marginBottom: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.background.default,
    borderRadius: theme.shape.borderRadius,
  },
  propertyWrapper: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: theme.spacing(2),
  },
  propertyItem: {
    display: 'inline-flex',
    alignItems: 'center',
    padding: theme.spacing(0.75, 1.5),
    margin: theme.spacing(0.5),
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
    minWidth: '200px',
    maxWidth: 'fit-content',
    flexGrow: 0,
  },
  propertyLabel: {
    fontWeight: 'bold',
    color: theme.palette.text.secondary,
    marginRight: theme.spacing(0.5),
    whiteSpace: 'nowrap',
  },
  propertyValue: {
    color: theme.palette.text.primary,
  },
  title: {
    marginBottom: theme.spacing(1),
    fontWeight: 500,
  },
}))

const ReadOnlyProperties: React.FC<ReadOnlyPropertiesProps> = ({
  configuration,
  selectedRecord,
}) => {
  const classes = useStyles()

  if (!configuration?.readOnly?.length || !selectedRecord) {
    return null
  }

  // Function to get value using JSONPath
  const getValueByPath = (root: string, path: string) => {
    try {
      const rootObject =
        root === 'source_properties'
          ? selectedRecord.sourceProperties
          : selectedRecord.data

      if (!rootObject) return '-'

      // Use jsonpath to extract the value
      const result = jsonpath.query(rootObject, path)
      return result?.[0] !== undefined ? result[0] : '-'
    } catch (error) {
      console.error('Error getting value by path:', error)
      return '-'
    }
  }

  return (
    <Paper elevation={0} className={classes.readOnlyContainer}>
      <Typography variant="subtitle1" className={classes.title}>
        بيانات مرجعيه
      </Typography>
      <Divider />
      <div className={classes.propertyWrapper}>
        {configuration.readOnly.map((property, index) => (
          <div key={index} className={classes.propertyItem}>
            <Typography variant="body2" className={classes.propertyLabel}>
              {property.title}:
            </Typography>
            <Typography variant="body1" className={classes.propertyValue}>
              {getValueByPath(property.root, property.jsonpath)}
            </Typography>
          </div>
        ))}
      </div>
    </Paper>
  )
}

export default ReadOnlyProperties
