import React from 'react'

import IconButton from '@material-ui/core/IconButton'
import { makeStyles } from '@material-ui/core/styles'
import Typography from '@material-ui/core/Typography'
import CloseIcon from '@material-ui/icons/Close'
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline'

interface ErrorBoxProps {
  errorMessage?: string
  handleDismiss: () => void
}

const useStyles = makeStyles((theme) => ({
  errorContainer: {
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.error.main,
  },
  errorIcon: {
    marginRight: theme.spacing(1),
  },
  closeButton: {
    marginLeft: 'auto', // Push the button to the right side
  },
}))

const defaultErrorMessage = 'Unhandled error'
export const ErrorBox: React.FC<ErrorBoxProps> = ({
  errorMessage,
  handleDismiss,
}) => {
  const classes = useStyles()

  return (
    <div className={classes.errorContainer}>
      <ErrorOutlineIcon className={classes.errorIcon} />
      <Typography variant="body2">
        {errorMessage ?? defaultErrorMessage}
      </Typography>
      <IconButton
        aria-label="Dismiss"
        className={classes.closeButton}
        onClick={handleDismiss}
      >
        <CloseIcon />
      </IconButton>
    </div>
  )
}
