import { Map } from 'ol'
import Feature from 'ol/Feature'
import Geolocation from 'ol/Geolocation'
import { Point } from 'ol/geom'
import VectorSource from 'ol/source/Vector'
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style'
import { useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from 'store'
import { toggleLayersDrawerOpen, updateErrorMessage } from 'store/layout'

import { useTranslation } from '@flint/locales'

const positionFeature = new Feature()
positionFeature.setStyle([
  new Style({
    image: new CircleStyle({
      radius: 15,
      fill: new Fill({
        color: 'rgba(51, 153, 204, 0.5)',
      }),
    }),
  }),
  new Style({
    image: new CircleStyle({
      radius: 6,
      fill: new Fill({
        color: '#3399CC',
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2,
      }),
    }),
  }),
])
export const moveToGPSLocation = ({ map, coordinates }) => {
  map.getView().setCenter(coordinates)
  map.getView().setZoom(20)
  positionFeature.setGeometry(new Point(coordinates))
}
export function LocationButton({
  map,
  locationSource,
}: {
  map: Map
  locationSource: VectorSource
}) {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const geolocationRef = useRef<Geolocation>(null)
  const { userLocationFlag } = useSelector((state: RootState) => state.layers)
  const handleGeoLocationError = (error) => {
    console.error('Tataba Error: user location error', error)
    geolocationRef.current = null
    dispatch(updateErrorMessage(t('user-location-error')))
    dispatch(toggleLayersDrawerOpen(false))
  }
  const startTracking = () => {
    if (!map) return
    if (!geolocationRef.current) {
      geolocationRef.current = new Geolocation({
        trackingOptions: {
          enableHighAccuracy: true,
        },
        projection: map.getView().getProjection(),
      })
      geolocationRef.current.on('error', handleGeoLocationError)
      geolocationRef.current.setTracking(true)
      geolocationRef.current.once('change:position', () => {
        moveToGPSLocation({
          map,
          coordinates: geolocationRef.current.getPosition(),
        })
        dispatch(toggleLayersDrawerOpen(false))
      })
    } else {
      geolocationRef.current.setTracking(true)
      moveToGPSLocation({
        map,
        coordinates: geolocationRef.current.getPosition(),
      })
      dispatch(toggleLayersDrawerOpen(false))
    }
  }
  useEffect(() => {
    startTracking()
  }, [userLocationFlag])
  useEffect(() => {
    locationSource.addFeature(positionFeature)
  }, [])

  // eslint-disable-next-line
  return <></>
}
