<svg xmlns="http://www.w3.org/2000/svg" width="248.813" height="247.942" viewBox="0 0 248.813 247.942">
  <g id="Questions-bro" transform="translate(-27.576 -36.534)">
    <g id="freepik--background-simple--inject-10" transform="translate(27.576 41.986)">
      <path id="Path_6072" data-name="Path 6072" d="M105.561,60.424S63.237,81.985,42.129,115.209A91.7,91.7,0,0,0,48.34,222.9c25.65,31.727,74.676,27.56,115.726,47.658s86.339,15.958,105.749-26.622-9.859-58.494-14.886-107.771S205.479,14.733,105.561,60.424Z" transform="translate(-27.576 -45.877)" fill="#cfd8dc"/>
      <path id="Path_6073" data-name="Path 6073" d="M105.561,60.424S63.237,81.985,42.129,115.209A91.7,91.7,0,0,0,48.34,222.9c25.65,31.727,74.676,27.56,115.726,47.658s86.339,15.958,105.749-26.622-9.859-58.494-14.886-107.771S205.479,14.733,105.561,60.424Z" transform="translate(-27.576 -45.877)" fill="#fff" opacity="0.7"/>
    </g>
    <g id="freepik--Plants--inject-10" transform="translate(74.646 80.425)">
      <path id="Path_6074" data-name="Path 6074" d="M118.776,374.179c1.676-.559,4.647-3.29,4.647-3.3s-2.391-1.391-2.553-1.458c-1.882-.793-5.988-1.218-7.479.519a2.715,2.715,0,0,0,.637,3.966C115.346,374.849,117.324,374.648,118.776,374.179Z" transform="translate(-112.265 -226.834)" fill="#9c9c9c"/>
      <path id="Path_6075" data-name="Path 6075" d="M171.2,326.273c.039,0-1.229-2.474-1.329-2.625-1.117-1.676-4.323-4.318-6.524-3.692a2.715,2.715,0,0,0-1.676,3.67c.6,1.5,2.357,2.424,3.832,2.832C167.236,326.938,171.2,326.279,171.2,326.273Z" transform="translate(-133.741 -205.26)" fill="#9c9c9c"/>
      <path id="Path_6076" data-name="Path 6076" d="M127.119,350.17c1.441-.944,3.43-4.1,3.586-4.351a47.761,47.761,0,0,0,5.619,3.05c1.117.525,2.279,1.022,3.441,1.5-.559-.145-1.067-.263-1.117-.274-2.028-.268-6.1.408-7.077,2.474a2.709,2.709,0,0,0,1.676,3.675c1.519.559,3.351-.173,4.653-1.011s3.044-3.513,3.5-4.223l1.341.559c2.151.855,4.3,1.676,6.379,2.592.732.313,1.441.654,2.156.983-.5-.123-.922-.223-.989-.229-2.028-.268-6.094.408-7.077,2.474a2.7,2.7,0,0,0,1.676,3.67c1.525.559,3.379-.173,4.653-1.011a17.027,17.027,0,0,0,3.418-4.089c.715.363,1.436.726,2.117,1.117.458.274.922.5,1.368.8l1.329.855c.821.559,1.676,1.162,2.469,1.743.559.4,1.072.793,1.6,1.184-.726-.318-2.162-.922-2.29-.955-1.961-.559-6.088-.519-7.368,1.374a2.7,2.7,0,0,0,1.084,3.882c1.419.765,3.351.341,4.753-.3,1.525-.7,3.91-3.4,4.178-3.737.866.654,1.7,1.3,2.48,1.916,2.268,1.793,4.156,3.407,5.586,4.67l1.117-1.017c-1.5-1.262-3.5-2.9-5.9-4.726-.877-.665-1.827-1.363-2.793-2.067a29.751,29.751,0,0,0,1.363-3.178,19.713,19.713,0,0,0,5.362-.832c.363,1.022.7,2.022,1.011,2.966.883,2.7,1.558,5.027,2.056,6.876l1.251-1.117c-.519-1.726-1.19-3.832-2.022-6.206-.363-1.039-.771-2.139-1.207-3.268.385-.218,3.491-2.028,4.469-3.391.888-1.24,1.676-3.067,1.2-4.614a2.715,2.715,0,0,0-3.6-1.8c-2.106.9-2.944,4.938-2.759,6.971,0,.123.279,1.413.458,2.234-.179-.464-.346-.91-.559-1.38-.369-.938-.765-1.86-1.184-2.854-.223-.475-.441-.95-.67-1.43s-.5-.966-.748-1.458c-.6-1.117-1.262-2.234-1.961-3.351.821-.324,3.709-1.519,4.781-2.614s2.123-2.793,1.877-4.374a2.7,2.7,0,0,0-3.284-2.329c-2.234.559-3.659,4.435-3.782,6.474,0,.123.067,1.6.123,2.407-.424-.659-.843-1.318-1.29-1.972-1.3-1.882-2.67-3.748-4.027-5.614-.419-.559-.827-1.145-1.234-1.72.29-.112,3.754-1.458,4.955-2.681,1.067-1.117,2.123-2.793,1.882-4.379a2.709,2.709,0,0,0-3.29-2.329c-2.234.559-3.659,4.435-3.782,6.474,0,.156.1,2.273.156,2.793-.9-1.268-1.793-2.536-2.62-3.821a46.716,46.716,0,0,1-3.027-5.39c.134-.05,3.754-1.441,4.982-2.7,1.067-1.117,2.123-2.793,1.882-4.374a2.7,2.7,0,0,0-3.284-2.329c-2.234.559-3.659,4.435-3.782,6.474,0,.134.073,1.748.128,2.514l-.123.034a34.482,34.482,0,0,1-2.022-5.882c-.285-1.117-.5-2.184-.681-3.229.7-.19,3.91-1.089,5.128-2.1s2.413-2.53,2.352-4.145a2.715,2.715,0,0,0-3.016-2.676c-2.262.318-4.122,4.005-4.469,6.021,0,.106-.089,1.24-.128,2.067-.073-.475-.162-.961-.223-1.419-.24-1.715-.357-3.3-.452-4.7-.045-.8-.067-1.525-.084-2.2.586-.559,2.944-2.793,3.558-4.206s.978-3.351.179-4.759a2.709,2.709,0,0,0-3.91-.989c-1.866,1.329-1.815,5.457-1.2,7.407.056.168,1.19,2.7,1.223,2.676l.034-.034c0,.648,0,1.346.045,2.111.061,1.408.14,3,.346,4.726a48.193,48.193,0,0,0,.927,5.53,34.944,34.944,0,0,0,1.91,5.982l.034.067c-.436-.508-1.7-1.888-1.815-1.983-1.542-1.341-5.312-3.022-7.261-1.838a2.7,2.7,0,0,0-.642,3.977c.972,1.29,2.91,1.72,4.441,1.726,1.72,0,5.117-1.536,5.39-1.676a47.914,47.914,0,0,0,3.005,5.647c.648,1.056,1.335,2.106,2.039,3.145-.391-.425-.743-.8-.8-.855-1.542-1.335-5.312-3.022-7.261-1.838a2.7,2.7,0,0,0-.642,3.977c.966,1.3,2.91,1.72,4.441,1.726s4.469-1.251,5.239-1.592c.274.4.559.8.821,1.2,1.318,1.894,2.642,3.787,3.91,5.675.441.67.849,1.341,1.257,2.011-.346-.38-.642-.693-.693-.737-1.542-1.341-5.312-3.022-7.261-1.838a2.7,2.7,0,0,0-.637,3.977c.966,1.29,2.91,1.72,4.435,1.726a16.476,16.476,0,0,0,5.105-1.53c.4.7.8,1.4,1.156,2.095.235.48.492.927.693,1.424s.43.972.637,1.447c.374.938.76,1.9,1.117,2.821.24.637.458,1.251.676,1.866-.425-.67-1.3-1.961-1.38-2.061a10.49,10.49,0,0,0-4.469-2.882,3.049,3.049,0,0,0-.425-.424,2.709,2.709,0,0,0-4,.48c-1.262,1.91.268,5.742,1.547,7.34.073.095,1.005,1.022,1.6,1.6l-1.262-.821c-.821-.559-1.676-1.117-2.558-1.726l-1.346-.827c-.447-.285-.95-.531-1.43-.8-1.117-.6-2.285-1.162-3.469-1.676a18.256,18.256,0,0,0,2.542-4.815c.307-1.508.235-3.486-.843-4.686a2.709,2.709,0,0,0-4.027-.129c-1.536,1.676-.6,5.714.413,7.485.067.112.938,1.3,1.436,1.938-.715-.313-1.43-.631-2.162-.927-2.123-.855-4.3-1.676-6.457-2.463-.665-.246-1.324-.5-1.983-.754.184-.251,2.329-3.284,2.653-4.971.29-1.5.24-3.485-.843-4.686a2.709,2.709,0,0,0-4.027-.128c-1.536,1.676-.6,5.714.419,7.485.073.134,1.335,1.838,1.676,2.234-1.452-.559-2.893-1.117-4.3-1.737a46.625,46.625,0,0,1-5.5-2.826c.084-.117,2.34-3.273,2.67-5,.285-1.5.235-3.486-.843-4.686a2.709,2.709,0,0,0-4.027-.128c-1.536,1.676-.6,5.714.413,7.485.067.117,1.028,1.419,1.5,2.028l-.089.095a34.788,34.788,0,0,1-4.927-3.793c-.855-.765-1.625-1.542-2.357-2.318.48-.559,2.648-3.055,3.122-4.58s.614-3.441-.324-4.748a2.715,2.715,0,0,0-3.994-.559c-1.709,1.519-1.223,5.619-.408,7.49.045.1.614,1.084,1.039,1.793-.324-.352-.665-.709-.972-1.056-1.117-1.3-2.117-2.553-2.966-3.675-.48-.642-.9-1.229-1.29-1.782.2-.782.933-3.938.654-5.474s-1.039-3.351-2.48-4.066a2.7,2.7,0,0,0-3.8,1.329c-.821,2.134,1.5,5.547,3.083,6.831.14.112,2.486,1.6,2.5,1.558a.359.359,0,0,0,0-.05c.363.559.76,1.117,1.2,1.743.827,1.117,1.776,2.424,2.9,3.748a48.869,48.869,0,0,0,3.826,4.106,35.55,35.55,0,0,0,4.888,3.91l.067.039c-.642-.184-2.463-.637-2.609-.654-2.028-.268-6.094.408-7.077,2.474a2.709,2.709,0,0,0,1.676,3.675C123.991,351.723,125.845,351.013,127.119,350.17Z" transform="translate(-111.844 -195.055)" fill="#9c9c9c"/>
      <path id="Path_6077" data-name="Path 6077" d="M325.212,132.076c1.2,2,5.5,4.932,5.508,4.932s1.072-3.424,1.117-3.653c.458-2.609-.19-7.926-2.793-9.311a3.513,3.513,0,0,0-4.854,1.944C323.38,127.931,324.2,130.378,325.212,132.076Z" transform="translate(-205.433 -118.661)" fill="#9c9c9c"/>
      <path id="Path_6078" data-name="Path 6078" d="M400.408,184.581c0,.05,2.793-2.268,2.944-2.435,1.827-1.927,4.217-6.7,2.793-9.323a3.513,3.513,0,0,0-5.122-1.056c-1.726,1.19-2.391,3.681-2.486,5.658C398.431,179.749,400.408,184.575,400.408,184.581Z" transform="translate(-238.395 -139.583)" fill="#9c9c9c"/>
      <path id="Path_6079" data-name="Path 6079" d="M303.211,141.441c1.614,1.547,6.183,3.156,6.547,3.279a62.267,62.267,0,0,0-2.234,7.993c-.341,1.575-.637,3.178-.911,4.787v-1.525c-.246-2.636-2.273-7.6-5.172-8.239a3.513,3.513,0,0,0-4.173,3.156c-.246,2.083,1.2,4.228,2.625,5.586s5.318,2.843,6.345,3.212c-.1.614-.207,1.234-.3,1.849-.464,2.966-.91,5.926-1.441,8.82-.184,1.017-.408,2.016-.626,3.016v-1.318c-.246-2.642-2.273-7.6-5.172-8.245a3.513,3.513,0,0,0-4.167,3.162c-.246,2.078,1.19,4.223,2.62,5.586s4.943,2.692,6.144,3.139c-.251,1.017-.5,2.033-.793,3.005-.218.659-.374,1.307-.626,1.961l-.7,1.927c-.486,1.207-.983,2.458-1.491,3.631-.346.81-.693,1.581-1.039,2.357.2-1.011.559-3,.559-3.167.156-2.648-1.117-7.854-3.865-8.937a3.513,3.513,0,0,0-4.6,2.491c-.559,2.016.559,4.357,1.743,5.926,1.329,1.732,5.424,3.944,5.938,4.217-.559,1.279-1.151,2.525-1.709,3.687-1.62,3.391-3.117,6.239-4.3,8.423l1.614,1.151c1.173-2.234,2.67-5.256,4.279-8.831.592-1.3,1.2-2.7,1.81-4.15a36.993,36.993,0,0,0,4.413.81c.279,2.234,2.329,6.055,2.6,6.547-1.19.754-2.352,1.475-3.463,2.134-3.156,1.9-5.932,3.43-8.1,4.586l1.759,1.257c2.039-1.151,4.508-2.6,7.261-4.34,1.212-.76,2.491-1.6,3.793-2.469.385.424,3.569,3.832,5.586,4.681,1.827.765,4.362,1.24,6.178.184a3.508,3.508,0,0,0,1.246-5.077c-1.743-2.407-7.094-2.3-9.619-1.48-.145.05-1.7.754-2.676,1.218.531-.358,1.05-.7,1.592-1.078,1.078-.737,2.134-1.5,3.268-2.318l1.62-1.257c.559-.413,1.078-.91,1.625-1.369q1.868-1.625,3.647-3.441c.648.938,2.994,4.251,4.686,5.29s4.128,1.888,6.077,1.117a3.513,3.513,0,0,0,2.005-4.832c-1.357-2.642-6.669-3.351-9.284-2.921-.162.028-2.005.559-3.011.849.709-.721,1.424-1.441,2.117-2.2,2.011-2.178,3.977-4.469,5.943-6.7.6-.693,1.218-1.374,1.821-2.056.229.335,2.927,4.329,4.826,5.5,1.676,1.033,4.128,1.888,6.083,1.117a3.513,3.513,0,0,0,2-4.832c-1.357-2.642-6.669-3.351-9.284-2.921-.2.028-2.849.782-3.513,1.005,1.341-1.508,2.687-3,4.078-4.418a62,62,0,0,1,5.949-5.385c.106.156,2.9,4.34,4.848,5.535,1.676,1.033,4.128,1.882,6.083,1.117a3.513,3.513,0,0,0,2-4.832c-1.357-2.642-6.669-3.351-9.284-2.916-.173,0-2.19.6-3.145.888l-.078-.151a45.463,45.463,0,0,1,6.865-4.251c1.318-.681,2.614-1.257,3.91-1.793.441.832,2.5,4.614,4.133,5.882s3.91,2.329,5.921,1.782a3.513,3.513,0,0,0,2.519-4.58c-1.061-2.793-6.256-4.066-8.9-3.91-.145,0-1.6.251-2.659.436.559-.229,1.173-.48,1.732-.693,2.1-.8,4.066-1.4,5.82-1.921.994-.29,1.9-.559,2.754-.748.86.592,4.351,2.933,6.351,3.3s4.53.274,6.072-1.145a3.513,3.513,0,0,0,.123-5.228c-2.234-1.972-7.423-.721-9.708.62-.2.117-3.078,2.29-3.033,2.324l.05.034c-.821.19-1.7.408-2.664.659-1.76.486-3.754,1.05-5.876,1.8a63.15,63.15,0,0,0-6.736,2.793,46.257,46.257,0,0,0-7.021,4.133l-.073.061c.514-.7,1.9-2.7,1.989-2.865,1.251-2.34,2.3-7.591.235-9.73a3.525,3.525,0,0,0-5.223.335c-1.352,1.6-1.335,4.184-.9,6.116.492,2.178,3.413,6.038,3.647,6.345a62.3,62.3,0,0,0-6.278,5.429c-1.156,1.117-2.279,2.3-3.4,3.491.43-.62.8-1.179.849-1.262,1.251-2.335,2.3-7.591.235-9.725a3.508,3.508,0,0,0-5.217.335c-1.357,1.6-1.341,4.178-.9,6.111s2.871,5.306,3.525,6.178l-1.274,1.38c-2.022,2.234-4.027,4.441-6.066,6.558-.715.748-1.452,1.458-2.184,2.173.38-.559.7-1.017.737-1.117,1.251-2.335,2.3-7.591.235-9.725a3.519,3.519,0,0,0-5.223.33c-1.352,1.6-1.335,4.184-.9,6.116s2.631,4.977,3.407,6.021c-.771.7-1.542,1.413-2.324,2.067-.559.43-1.033.883-1.6,1.29l-1.648,1.218c-1.072.743-2.178,1.508-3.245,2.206-.743.486-1.452.938-2.173,1.4.726-.737,2.111-2.206,2.234-2.34a13.674,13.674,0,0,0,2.368-6.468,4.214,4.214,0,0,0,.413-.659,3.519,3.519,0,0,0-1.765-4.927c-2.793-1.045-7.189,1.994-8.837,4.072-.1.123-1.005,1.57-1.564,2.491.246-.592.492-1.162.737-1.776.492-1.207.95-2.424,1.441-3.737.218-.642.441-1.29.659-1.944s.4-1.352.6-2.033c.441-1.6.81-3.229,1.145-4.888,1.056.43,4.837,1.9,6.826,1.832s4.469-.7,5.692-2.419a3.513,3.513,0,0,0-1-5.133c-2.586-1.458-7.407.883-9.351,2.681-.123.117-1.374,1.564-2.044,2.374.19-.994.391-1.989.559-3.005.469-2.933.855-5.91,1.257-8.881.117-.91.251-1.815.38-2.72.374.156,4.832,2,7.06,1.922,1.977-.067,4.469-.7,5.686-2.419a3.513,3.513,0,0,0-.994-5.133c-2.592-1.452-7.412.883-9.356,2.687-.145.134-1.944,2.234-2.374,2.793.285-1.994.559-3.983.961-5.932a62.567,62.567,0,0,1,2.005-7.781c.173.073,4.815,2.022,7.1,1.944,1.977-.073,4.469-.7,5.686-2.419a3.519,3.519,0,0,0-.994-5.139c-2.592-1.452-7.407.883-9.356,2.687-.128.117-1.5,1.709-2.134,2.48l-.145-.084a45.056,45.056,0,0,1,3.351-7.329c.726-1.3,1.486-2.5,2.234-3.647.827.452,4.631,2.469,6.7,2.631,1.977.145,4.53-.212,5.921-1.782a3.513,3.513,0,0,0-.43-5.212c-2.413-1.732-7.457.061-9.591,1.642-.112.084-1.2,1.084-1.972,1.827.352-.514.709-1.05,1.061-1.536,1.307-1.821,2.62-3.413,3.787-4.815.676-.788,1.3-1.491,1.888-2.139,1.044,0,5.251.045,7.116-.754s3.91-2.273,4.435-4.307a3.513,3.513,0,0,0-2.793-4.429c-2.938-.425-6.591,3.5-7.759,5.876-.1.207-1.3,3.608-1.251,3.608h.067c-.559.614-1.2,1.279-1.86,2.022-1.2,1.374-2.553,2.944-3.91,4.742A62.776,62.776,0,0,0,313.428,137a45.945,45.945,0,0,0-3.569,7.323l-.034.095c.05-.871.095-3.3.078-3.491-.246-2.642-2.273-7.6-5.172-8.239a3.513,3.513,0,0,0-4.167,3.156C300.346,137.928,301.781,140.073,303.211,141.441Z" transform="translate(-190.745 -114.693)" fill="#9c9c9c"/>
    </g>
    <g id="freepik--question-marks-2--inject-10" transform="translate(46.815 47.702)">
      <path id="Path_6080" data-name="Path 6080" d="M365.729,312.822v-1.871c0-9.635,12.2-10.054,12.2-16.914,0-2.977-2.424-4.921-6.033-4.921-3.949,0-6.446,2.151-6.446,5.2v.765c0,.76-.413,1.117-1.179,1.117l-7.412-.279c-.693,0-1.179-.346-1.179-.827v-.765c0-8.379,6.653-14.138,16.428-14.138,9.563,0,15.729,5.407,15.729,13.512,0,11.507-12.406,11.579-12.406,17.874v1.246A1.061,1.061,0,0,1,374.253,314h-7.345a1.061,1.061,0,0,1-1.179-1.179Zm-.626,11.172a5.939,5.939,0,0,1,11.853,0,5.939,5.939,0,0,1-11.853-.011Z" transform="translate(-191.649 -155.025)" fill="#263238"/>
      <path id="Path_6081" data-name="Path 6081" d="M104.6,174.094v-1.625c0-8.379,10.613-8.731,10.613-14.691,0-2.592-2.106-4.273-5.234-4.273-3.435,0-5.586,1.866-5.586,4.513v.665c0,.659-.357.961-1.022.961l-6.44-.24c-.6,0-1.022-.3-1.022-.721v-.665c0-7.261,5.776-12.289,14.266-12.289,8.306,0,13.668,4.7,13.668,11.73,0,9.993-10.78,10.054-10.78,15.534v1.084a.922.922,0,0,1-1.022,1.022h-6.418a.922.922,0,0,1-1.022-1.005Zm-.559,9.691a5.159,5.159,0,1,1,5.117,4.815,4.977,4.977,0,0,1-5.1-4.815Z" transform="translate(-76.98 -95.671)" fill="#cfd8dc"/>
      <path id="Path_6082" data-name="Path 6082" d="M349.343,70.437v-.821c0-4.228,5.351-4.413,5.351-7.423,0-1.307-1.067-2.162-2.648-2.162-1.732,0-2.826.944-2.826,2.285v.335c0,.335-.184.486-.519.486l-3.251-.123c-.307,0-.519-.151-.519-.363v-.335c0-3.681,2.921-6.206,7.211-6.206,4.195,0,6.9,2.374,6.9,5.932,0,5.027-5.446,5.077-5.446,7.82v.559a.464.464,0,0,1-.519.514h-3.223a.458.458,0,0,1-.514-.5Zm-.274,4.9a2.609,2.609,0,1,1,2.6,2.43,2.514,2.514,0,0,1-2.6-2.43Z" transform="translate(-186.904 -56.11)" fill="#cfd8dc"/>
      <path id="Path_6083" data-name="Path 6083" d="M67.595,269.733V268.7c0-5.329,6.748-5.558,6.748-9.356,0-1.648-1.341-2.72-3.351-2.72-2.184,0-3.564,1.19-3.564,2.877v.419c0,.425-.229.614-.654.614l-4.1-.151c-.385,0-.654-.2-.654-.464V259.5c0-4.636,3.681-7.82,9.088-7.82,5.29,0,8.7,2.988,8.7,7.474,0,6.362-6.859,6.4-6.859,9.892v.687a.587.587,0,0,1-.654.654H68.237a.586.586,0,0,1-.642-.654Zm-.346,6.172a3.284,3.284,0,1,1,3.262,3.067,3.173,3.173,0,0,1-3.273-3.067Z" transform="translate(-62.02 -142.439)" fill="none" stroke="#263238" stroke-width="1"/>
      <path id="Path_6084" data-name="Path 6084" d="M161,101.567v-.709c0-3.664,4.642-3.826,4.642-6.435,0-1.117-.922-1.871-2.3-1.871-1.5,0-2.452.816-2.452,1.977v.29c0,.29-.156.419-.447.419l-2.793-.106c-.263,0-.447-.128-.447-.313v-.29c0-3.189,2.53-5.379,6.245-5.379s5.926,2.056,5.926,5.139c0,4.379-4.72,4.407-4.72,6.8v.475a.4.4,0,0,1-.447.447h-2.793a.4.4,0,0,1-.419-.447Zm-.235,4.245A2.234,2.234,0,1,1,163,107.924a2.178,2.178,0,0,1-2.251-2.1Z" transform="translate(-104.039 -70.695)" fill="#263238"/>
      <path id="Path_6085" data-name="Path 6085" d="M309,375.717v-.709c0-3.664,4.642-3.821,4.642-6.435,0-1.117-.922-1.871-2.3-1.871-1.5,0-2.452.816-2.452,1.977v.29c0,.29-.156.425-.447.425l-2.793-.106c-.263,0-.447-.134-.447-.318v-.29c0-3.189,2.53-5.379,6.245-5.379s5.988,2.056,5.988,5.144c0,4.374-4.72,4.4-4.72,6.8v.475a.408.408,0,0,1-.447.452h-2.793a.408.408,0,0,1-.48-.452Zm-.235,4.245A2.234,2.234,0,1,1,311,382.074a2.179,2.179,0,0,1-2.229-2.111Z" transform="translate(-169.37 -191.711)" fill="none" stroke="#263238" stroke-width="1"/>
    </g>
    <g id="freepik--question-mark-1--inject-10" transform="translate(102.874 120.685)">
      <path id="Path_6086" data-name="Path 6086" d="M246.042,395.356c-.039-.117-.078-.235-.123-.352s-.078-.24-.123-.358-.078-.235-.123-.352-.078-.24-.123-.357-.078-.235-.123-.352-.078-.24-.123-.357l-.123-.352c-.039-.123-.084-.24-.123-.357l-.123-.352c-.039-.123-.084-.24-.123-.358l-.123-.352c-.039-.123-.084-.24-.123-.358l-.117-.374c-.039-.123-.084-.24-.123-.358l-.128-.352c-.039-.123-.078-.24-.123-.357s-.078-.235-.123-.352-.078-.24-.123-.357-.078-.235-.123-.352a14.677,14.677,0,0,0-27.052-1.184c-.056.112-.106.229-.156.341l-.151.346c-.056.112-.106.223-.156.341s-.106.229-.156.341l-.151.341c-.056.117-.106.229-.156.346s-.106.223-.156.341-.106.223-.156.341l-.151.341c-.056.112-.106.229-.156.341s-.106.229-.156.341l-.151.346c-.056.112-.106.223-.156.341s-.106.229-.156.341l-.151.341c-.056.117-.106.229-.156.346s-.106.223-.156.341l-.151.341c-.056.112-.106.229-.156.341s-.106.229-.156.346a15.2,15.2,0,0,0-1.441,6.524c0,8.993,7.764,16.344,17.366,16.344,9.4,0,17.567-7.351,17.567-16.344a14.757,14.757,0,0,0-.888-5.027A3.085,3.085,0,0,0,246.042,395.356Z" transform="translate(-184.336 -271.621)" fill="#263238"/>
      <path id="Path_6087" data-name="Path 6087" d="M210.8,186.8c-28.806,0-48.417,16.953-48.417,41.675v2.234a2.145,2.145,0,0,0,1.061,1.821,2.743,2.743,0,0,0,.391.313,13499.65,13499.65,0,0,1,.793.626,2.234,2.234,0,0,0,.4.313,2.4,2.4,0,0,0,.391.313,2.474,2.474,0,0,0,.4.318,2.794,2.794,0,0,0,.4.313,2.512,2.512,0,0,0,.4.313,2.465,2.465,0,0,0,.391.313,2.6,2.6,0,0,0,.43.335,3.147,3.147,0,0,0,.363.285,2.8,2.8,0,0,0,.4.313,2.742,2.742,0,0,0,.391.313,2.792,2.792,0,0,0,.4.313,2.513,2.513,0,0,0,.4.313,2.466,2.466,0,0,0,.391.313,2.513,2.513,0,0,0,.4.313,2.463,2.463,0,0,0,.391.313,2.8,2.8,0,0,0,.4.313,2.463,2.463,0,0,0,.391.313,3.29,3.29,0,0,0,2.346.816l17.964.67c1.849,0,2.854-.838,2.854-2.687v-1.843c0-7.39,6.044-12.59,15.64-12.59,8.731,0,14.607,4.7,14.607,11.92a12.911,12.911,0,0,1-.235,2.455c-9.015,11.817-32.481,17.028-32.481,39.969v5.519a3.128,3.128,0,0,0,3.474,3.469h21.628a3.13,3.13,0,0,0,3.474-3.469v-3.681c0-18.589,36.57-18.8,36.57-52.707C257.176,202.731,238.989,186.8,210.8,186.8Z" transform="translate(-162.38 -186.783)" fill="#263238"/>
      <path id="Path_6088" data-name="Path 6088" d="M192.006,283.012v-5.519c0-28.4,35.955-29.6,35.955-49.847,0-8.786-7.15-14.523-17.774-14.523-11.646,0-18.992,6.334-18.992,15.322v2.234c0,2.234-1.229,3.273-3.474,3.273l-21.863-.821c-2.039,0-3.469-1.022-3.469-2.452v-2.234c0-24.723,19.612-41.675,48.417-41.675,28.191,0,46.362,15.931,46.362,39.838,0,33.911-36.57,34.118-36.57,52.707V283a3.13,3.13,0,0,1-3.474,3.469H195.48A3.1,3.1,0,0,1,192.006,283.012Zm-1.838,32.917c0-8.987,7.965-16.344,17.567-16.344,9.194,0,17.366,7.356,17.366,16.344s-8.189,16.316-17.567,16.316C197.921,332.245,190.168,324.894,190.168,315.929Z" transform="translate(-162.384 -186.77)" fill="#cfd8dc" stroke="#cfd8dc" stroke-width="1"/>
      <ellipse id="Ellipse_657" data-name="Ellipse 657" cx="30.504" cy="6.82" rx="30.504" ry="6.82" transform="translate(13.752 150.151)" fill="#263238"/>
    </g>
    <g id="freepik--Character--inject-10" transform="translate(115.866 37.063)">
      <path id="Path_6089" data-name="Path 6089" d="M193.589,190.85l-1.167,2.547s-6.781.732-6.781,1.24.073,4.011.441,4.156a1.2,1.2,0,0,0,.871,0l9.987-2.553.95-4.519Z" transform="translate(-185.64 -104.948)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6090" data-name="Path 6090" d="M192.169,198.21a24.271,24.271,0,0,1-3.994,2.659c-1.614.726-1.676,1.827-1.162,1.827a53.848,53.848,0,0,0,5.072-1.91s4.139-1.084,4.737-1.413.665-2.162.665-2.162" transform="translate(-186.12 -107.756)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6091" data-name="Path 6091" d="M204.966,103.23,200.8,135.169l-2.9,17.293,6.435,1.519,4.8-20.455,2.273-12.875Z" transform="translate(-191.052 -66.271)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6092" data-name="Path 6092" d="M264.477,59.954l-2.542-.4L256.73,57.58l-.19,6.183,8.071.743Z" transform="translate(-216.937 -46.119)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6093" data-name="Path 6093" d="M212.384,263.368a78.364,78.364,0,0,0-5.3,6.926c-2.151,3.284-1.391,6.815,1.262,7.7s6.055-1.519,6.943-3.413.631-2.9,2.016-4.793,2.525-3.787,2.022-6.312S213.747,261.6,212.384,263.368Z" transform="translate(-194.554 -136.271)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6094" data-name="Path 6094" d="M215.046,282.178c-.179-.38-.385-.782-.631-1.212-2.028-3.569-5.586-3.284-7.82-2.62-1.469,3-.631,5.982,1.776,6.787S213.885,283.971,215.046,282.178Z" transform="translate(-194.569 -143.39)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6095" data-name="Path 6095" d="M214.474,275.18c-.754,1.117-1.765,4.167-5.681,2.9A5.4,5.4,0,0,1,206,276.326a3.681,3.681,0,0,0,2.441,3.022c2.648.883,6.055-1.519,6.943-3.413s.631-2.9,2.016-4.793,2.525-3.787,2.022-6.312c0,3.027-1.117,3.53-2.525,5.172S215.233,274.041,214.474,275.18Z" transform="translate(-194.627 -137.605)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6096" data-name="Path 6096" d="M278.667,263.368a78.359,78.359,0,0,1,5.3,6.926c2.151,3.284,1.391,6.815-1.262,7.7s-6.055-1.519-6.943-3.413-.631-2.9-2.016-4.793-2.525-3.787-2.022-6.312S277.282,261.6,278.667,263.368Z" transform="translate(-223.586 -136.271)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6097" data-name="Path 6097" d="M279.52,282.178c.173-.38.38-.782.626-1.212,2.028-3.569,5.586-3.284,7.82-2.62,1.469,3,.631,5.982-1.776,6.787S280.676,283.971,279.52,282.178Z" transform="translate(-227.081 -143.39)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6098" data-name="Path 6098" d="M276.633,275.18c.754,1.117,1.765,4.167,5.681,2.9a5.4,5.4,0,0,0,2.793-1.76,3.681,3.681,0,0,1-2.441,3.022c-2.648.883-6.055-1.519-6.943-3.413s-.631-2.9-2.016-4.793-2.525-3.787-2.022-6.312c0,3.027,1.117,3.53,2.525,5.172S275.874,274.041,276.633,275.18Z" transform="translate(-223.569 -137.605)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6099" data-name="Path 6099" d="M256.344,178.858c0-2.273-3.91-4.8-3.91-4.8-5.893-5.993-16.528-5.2-20.455-4.664-3.91-.559-14.556-1.329-20.449,4.664,0,0-3.91,2.525-3.91,4.8s4.927,43.3,4.927,43.3-.128,2.145,3.407,2.4,5.027-1.765,5.027-1.765l1.391-34.715s5.681-4.044,6.7-4.167c.369-.05.849-.128,1.385-.2v.067a7.429,7.429,0,0,1,3.033,0v-.067c.559.067,1.011.145,1.385.2,1.011.123,6.7,4.167,6.7,4.167l1.391,34.715A5.748,5.748,0,0,0,248,224.555c3.513-.251,3.407-2.4,3.407-2.4S256.344,181.131,256.344,178.858Z" transform="translate(-195.343 -95.313)" fill="#263238" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6100" data-name="Path 6100" d="M268.358,194.05s-.76-8.334,2.9-11.49" transform="translate(-222.125 -101.289)" fill="none" stroke="#707070" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6101" data-name="Path 6101" d="M232.162,192.854s.38-6.943-.38-9.468a7.4,7.4,0,0,0-2.022-3.536" transform="translate(-205.116 -100.092)" fill="none" stroke="#707070" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6102" data-name="Path 6102" d="M242.42,173.52s4.038,4.67,4.8,8.585" transform="translate(-210.704 -97.298)" fill="none" stroke="#707070" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6103" data-name="Path 6103" d="M256.245,174s-2.273,2.525-2.525,4.418" transform="translate(-215.692 -97.51)" fill="none" stroke="#707070" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6104" data-name="Path 6104" d="M239.789,85.98s-3.659-.508-4.921.754-5.558,5.915-5.558,5.915l2.273,40.145a31.557,31.557,0,0,0,9.6,2.022c4.418,0,9.468-1.642,10.613-2.525s.128-43.932.128-43.932S242.543,85.6,239.789,85.98Z" transform="translate(-204.917 -58.611)" fill="#cfd8dc" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6105" data-name="Path 6105" d="M258.47,75.5a46.027,46.027,0,0,1,5.8-.508c2.525,0,5.027.128,5.027.128l6.312-6.061-12.7-2.9,1.011-5.8s9.339-.631,14.009.251,11.993,3.156,12.372,3.156.883,1.391.5,2.4S278.55,80.294,275,83.7A87.088,87.088,0,0,0,268.44,90.9l-1.62,28.152a44.76,44.76,0,0,1,4.927,3.027c.76.754,1.642,3.787,1.642,3.787s-7.7-3.156-9.725-3.407-3.279-.38-3.659-1.117-3.536-34.464-3.536-34.464l.128-10.73S256.576,75.5,258.47,75.5Z" transform="translate(-216.906 -47.244)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6106" data-name="Path 6106" d="M268.22,118.6l-6.82.883.631,2.525,3.413.883,3.279-1.514Z" transform="translate(-219.082 -73.055)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6107" data-name="Path 6107" d="M279.414,91.48,277.9,104.735,281.816,93.5" transform="translate(-226.366 -61.084)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6108" data-name="Path 6108" d="M290.78,75.112s3.156.631,7.954-1.262" transform="translate(-232.051 -53.301)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6109" data-name="Path 6109" d="M296.66,77.293a28.526,28.526,0,0,0,6.183-.5" transform="translate(-234.647 -54.599)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6110" data-name="Path 6110" d="M240.39,85.83l.631,9.216a6.072,6.072,0,0,0,4.921,2.4c3.156-.123,3.536-2.4,3.536-2.4V87.472Z" transform="translate(-209.808 -58.59)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6111" data-name="Path 6111" d="M256.681,90.379l-.251,7.585,2.653-4.167s2.9,3.659,3.659,3.536-.5-1.642-.754-3.027-2.4-6.317-2.653-7.7-2.653-1.642-2.793-1.011a6.447,6.447,0,0,0,0,1.642Z" transform="translate(-216.888 -58.341)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6112" data-name="Path 6112" d="M227.7,97.929s.5,35.347-.76,35.749-6.312.508-9.5,2.022-4.67,2.145-4.67,2.145,1.514-3.659,3.284-5.3a17.434,17.434,0,0,1,3.91-2.653l-3.279-16.914s-6.061-16.037-6.189-16.919,11.49-7.446,13.886-8.585,3.033-.123,3.033-.123Z" transform="translate(-196.613 -59.076)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6113" data-name="Path 6113" d="M232.97,84.34s-3.027,9.6-3.284,10.859,0,1.262.888.374,3.156-3.027,3.156-3.027l2.4,4.038L236,89.139v-4.3S235.874,83.078,232.97,84.34Z" transform="translate(-205.041 -57.731)" fill="#7d7d7d" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6114" data-name="Path 6114" d="M224.918,121.77h7.446l-.123,3.027-3.284,1.134L224.79,124.8Z" transform="translate(-202.922 -74.455)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6115" data-name="Path 6115" d="M246.674,59.037s1.765,7.7,2.145,8.937-2.016,6.189-2.793,7.323a3.675,3.675,0,0,1-2.793,1.765,27.629,27.629,0,0,1-6.82-2.4c-1.139-.76-2.268-1.642-2.268-1.642a3.033,3.033,0,0,1-3.284-.5c-1.514-1.391-3.536-3.033-3.033-4.167a2.966,2.966,0,0,1,2.9-1.765c1.391,0,.888-.128.888-.128S229.1,62.8,229.1,59.9s5.424-5.027,8.58-5.027a16.858,16.858,0,0,1,6.312,1.514Z" transform="translate(-204.228 -44.923)" fill="#fff" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6116" data-name="Path 6116" d="M252.921,67.52l2.273,4.3-2.273,1.514-1.011-1.262" transform="translate(-214.893 -50.507)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6117" data-name="Path 6117" d="M260.081,74.53s.123,1.011-1.391,2.653" transform="translate(-217.886 -53.602)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6118" data-name="Path 6118" d="M248.547,69.5c0,.592-.257,1.072-.559,1.072s-.559-.48-.559-1.072.257-1.072.559-1.072S248.547,68.91,248.547,69.5Z" transform="translate(-212.916 -50.909)" fill="#263238"/>
      <ellipse id="Ellipse_658" data-name="Ellipse 658" cx="0.57" cy="1.072" rx="0.57" ry="1.072" transform="translate(39.033 15.499)" fill="#263238"/>
      <path id="Path_6119" data-name="Path 6119" d="M230.73,44.607s-7.943,2.9-8.205,6.569,7.072,8.708,7.451,9.339,1.514-.76,1.514-.76a2.933,2.933,0,0,1-.508-2.4c.38-1.262,2.022-2.793,2.022-3.407s-1.894.5-1.894-1.391,4.67-3.027,7.446-2.9,5.178,4.8,7.2,2.525-.888-2.9-.888-2.9,3.91-.508,3.91-2.525a3.684,3.684,0,0,0-2.9-3.413c-.883,0-3.279,1.519-3.279-.123s2.4-2.4.123-5.027-5.172,0-5.172,1.011-.76,2.9-1.262,2.9S234.26,39.837,232.5,41.1a4.681,4.681,0,0,0-1.765,3.508Z" transform="translate(-201.919 -37.063)" fill="#263238" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <line id="Line_322" data-name="Line 322" y1="1.519" x2="4.038" transform="translate(32.096 15.75)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6120" data-name="Path 6120" d="M251.435,61.182a1.737,1.737,0,0,1,.179-1.162" transform="translate(-214.669 -47.197)" fill="none" stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_6121" data-name="Path 6121" d="M251.78,58.526a4.3,4.3,0,0,1,2.346-1.866" transform="translate(-214.836 -45.713)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    </g>
  </g>
</svg>
