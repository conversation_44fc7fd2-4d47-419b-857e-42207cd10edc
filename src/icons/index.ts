import ForwardsIcon from './images/forwards.png'
import MarkerIcon from './images/map-marker.png'

export { default as Icon1 } from './images/icon_1.png'
export { default as Icon2 } from './images/icon_2.png'
export { default as Icon3 } from './images/icon_3.png'
export { default as Icon4 } from './images/icon_4.png'
export { default as Icon5 } from './images/icon_5.png'
export { default as Icon6 } from './images/icon_6.png'
export { default as Icon7 } from './images/icon_7.png'
export { default as Icon8 } from './images/icon_8.png'
export { default as ImagePlaceholder } from './images/imagePlaceholder.png'
export { ReactComponent as GoogleStoreIcon } from './images/android-arabic.svg'
export { ReactComponent as AppleStoreIcon } from './images/apple-arabic.svg'
export { ReactComponent as TatabaLogo } from './images/tataba_logo.svg'
export { ReactComponent as Rased<PERSON>ogo } from './images/RasedLogo.svg'
export { ReactComponent as RasedLogoGreen } from './images/rasedLogoGreen.svg'
export { ReactComponent as TatabaAppLogo } from './images/drawer-tataba-logo.svg'
export { ReactComponent as NavigationPanaIcon } from './images/Navigation-pana.svg'
export { ReactComponent as DashboardIcon } from './images/dashboard.svg'
export { ReactComponent as LayerImort } from './images/import-layer.svg'
export { ReactComponent as LayerNew } from './images/new-layer.svg'
export { ReactComponent as HowToUseIcon } from './images/how-to-use.svg'
export { ReactComponent as AddBasicIcon } from './images/add_basic.svg'
export { ReactComponent as ClientAndOrder } from './images/clientandorder.svg'
export { ReactComponent as DocumentsIcon } from './images/paper-clip.svg'
export { ReactComponent as RedirectRequestIcon } from './images/redirect-request.svg'
export { ReactComponent as PropsDetailsIcon } from './images/props-details.svg'
export { ReactComponent as XlIcon } from './images/XlfileIcon.svg'
export { ReactComponent as PropTypesIcon } from './images/props-type.svg'
export { ReactComponent as SwapIcon } from './images/swap.svg'
export { ReactComponent as NoOrg } from './images/noOrg.svg'
export { ReactComponent as AddOffer } from './images/addOffer.svg'
export { ReactComponent as poiIcon } from './images/poi.svg'
export { ReactComponent as offersIcon } from './images/offers.svg'
export { ReactComponent as vdIcon } from './images/vdIcon.svg'
export { ReactComponent as motIcon } from './images/motIcon.svg'
export { ReactComponent as DoneIcon } from './images/done_icon.svg'
export { ReactComponent as PendingIcon } from './images/pending_icon.svg'
export { ReactComponent as AssignIcon } from './images/assign_icon.svg'

export { ReactComponent as Done } from './images/done.svg'
export { ReactComponent as Pending } from './images/pending.svg'
export { ReactComponent as Assign } from './images/assign.svg'

export { ReactComponent as CloseIcon } from './images/close.svg'
export { ReactComponent as White } from './images/whiteIcon.svg'
export { ReactComponent as TreeOutlinedIcon } from './images/treeOutlinedIcon.svg'
export { ReactComponent as ElrasedLogo } from './images/ElrasedLogo.svg'
export { ReactComponent as AvatarPlaceholder } from './images/Profileavatar.svg'
export { ReactComponent as SignOutIcon } from './images/signOutIcon.svg'

export { MarkerIcon }
export { ForwardsIcon }
