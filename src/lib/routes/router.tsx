import { createBrowserRouter, Navigate, Outlet } from 'react-router-dom'
import { useAuth } from 'react-oidc-context'
import { HomePage, ProjectSwitcher } from 'pages'
import { Vd, Offers, Poi, SimpleProject } from 'containers'
import App from 'App'
import { TestSchema1 } from 'components/TestSchema1'
import { TestSchema2 } from 'components/TestSchema2'
import { TestSchema3 } from 'components/TestSchema3'
import { TestSchema4 } from 'components/TestSchema4'
import { TestSchema5 } from 'components/TestSchema5'
import { TestSchemaICUOrder1 } from 'components/TestSchema_icu_order1'
import { userLoader } from './loaders'

const ProtectedRoute = () => {
  const { isAuthenticated } = useAuth()
  return isAuthenticated ? <Outlet /> : <Navigate to="/" replace={false} />
}

export const router = createBrowserRouter([
  {
    path: '',
    element: <App />,
    children: [
      { path: '/', element: <HomePage /> },
      // unified observer, plan 2 schemas
      { path: '/uo-plan-2-schema/1', element: <TestSchema5 /> },
      // unified observer, plan 4 schemas
      { path: '/uo-plan-4-schema/1', element: <TestSchema1 /> },
      { path: '/uo-plan-4-schema/2', element: <TestSchema2 /> },
      { path: '/uo-plan-4-schema/3', element: <TestSchema3 /> },
      { path: '/uo-plan-4-schema/4', element: <TestSchema4 /> },
      // ICU Order 1
      { path: '/icu-order-1/1', element: <TestSchemaICUOrder1 /> },
      {
        path: '*',
        element: <ProtectedRoute />,
        children: [
          { path: 'projects', element: <ProjectSwitcher /> },
          { path: 'vd', element: <Vd /> },
          {
            path: 'offers',
            element: <Offers />,
            loader: userLoader,
          },
          {
            path: 'poi',
            element: <Poi />,
            loader: userLoader,
          },
          {
            path: 'simple-project',
            element: <Outlet />,
            children: [
              { path: '', element: <Navigate to="/projects" /> },
              {
                path: ':projectId',
                element: <SimpleProject />,
                loader: userLoader,
              },
            ],
          },
        ],
      },
    ],
  },
])
