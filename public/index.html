<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.png" />
    <!--  Hotjar Tracking Code for  -->
    <script>
      // run hotjar script only in production
      if ('%REACT_APP_DEPLOYMENT%' === 'PRODUCTION') {
        ;(function (h, o, t, j, a, r) {
          h.hj =
            h.hj ||
            function () {
              ;(h.hj.q = h.hj.q || []).push(arguments)
            }
          h._hjSettings = { hjid: 3643825, hjsv: 6 }
          a = o.getElementsByTagName('head')[0]
          r = o.createElement('script')
          r.async = 1
          r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv
          a.appendChild(r)
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=')
      }
    </script>
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>منصة تتبع</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <script>
      window['_fs_host'] = 'fullstory.com'
      window['_fs_script'] = 'edge.fullstory.com/s/fs.js'
      window['_fs_org'] = 'o-220Z8M-na1'
      window['_fs_namespace'] = 'FS'
      !(function (m, n, e, t, l, o, g, y) {
        var s,
          f,
          a = (function (h) {
            return (
              !(h in m) ||
              (m.console &&
                m.console.log &&
                m.console.log(
                  'FullStory namespace conflict. Please set window["_fs_namespace"].'
                ),
              !1)
            )
          })(e)
        function p(b) {
          var h,
            d = []
          function j() {
            h &&
              (d.forEach(function (b) {
                var d
                try {
                  d = b[h[0]] && b[h[0]](h[1])
                } catch (h) {
                  return void (b[3] && b[3](h))
                }
                d && d.then ? d.then(b[2], b[3]) : b[2] && b[2](d)
              }),
              (d.length = 0))
          }
          function r(b) {
            return function (d) {
              h || ((h = [b, d]), j())
            }
          }
          return (
            b(r(0), r(1)),
            {
              then: function (b, h) {
                return p(function (r, i) {
                  d.push([b, h, r, i]), j()
                })
              },
            }
          )
        }
        a &&
          ((g = m[e] =
            (function () {
              var b = function (b, d, j, r) {
                function i(i, c) {
                  h(b, d, j, i, c, r)
                }
                r = r || 2
                var c,
                  u = /Async$/
                return u.test(b)
                  ? ((b = b.replace(u, '')),
                    'function' == typeof Promise ? new Promise(i) : p(i))
                  : h(b, d, j, c, c, r)
              }
              function h(h, d, j, r, i, c) {
                return b._api
                  ? b._api(h, d, j, r, i, c)
                  : (b.q && b.q.push([h, d, j, r, i, c]), null)
              }
              return (b.q = []), b
            })()),
          (y = function (b) {
            function h(h) {
              'function' == typeof h[4] && h[4](new Error(b))
            }
            var d = g.q
            if (d) {
              for (var j = 0; j < d.length; j++) h(d[j])
              ;(d.length = 0), (d.push = h)
            }
          }),
          (function () {
            ;((o = n.createElement(t)).async = !0),
              (o.crossOrigin = 'anonymous'),
              (o.src = 'https://' + l),
              (o.onerror = function () {
                y('Error loading ' + l)
              })
            var b = n.getElementsByTagName(t)[0]
            b && b.parentNode
              ? b.parentNode.insertBefore(o, b)
              : n.head.appendChild(o)
          })(),
          (function () {
            function b() {}
            function h(b, h, d) {
              g(b, h, d, 1)
            }
            function d(b, d, j) {
              h('setProperties', { type: b, properties: d }, j)
            }
            function j(b, h) {
              d('user', b, h)
            }
            function r(b, h, d) {
              j(
                {
                  uid: b,
                },
                d
              ),
                h && j(h, d)
            }
            ;(g.identify = r),
              (g.setUserVars = j),
              (g.identifyAccount = b),
              (g.clearUserCookie = b),
              (g.setVars = d),
              (g.event = function (b, d, j) {
                h(
                  'trackEvent',
                  {
                    name: b,
                    properties: d,
                  },
                  j
                )
              }),
              (g.anonymize = function () {
                r(!1)
              }),
              (g.shutdown = function () {
                h('shutdown')
              }),
              (g.restart = function () {
                h('restart')
              }),
              (g.log = function (b, d) {
                h('log', { level: b, msg: d })
              }),
              (g.consent = function (b) {
                h('setIdentity', { consent: !arguments.length || b })
              })
          })(),
          (s = 'fetch'),
          (f = 'XMLHttpRequest'),
          (g._w = {}),
          (g._w[f] = m[f]),
          (g._w[s] = m[s]),
          m[s] &&
            (m[s] = function () {
              return g._w[s].apply(this, arguments)
            }),
          (g._v = '2.0.0'))
      })(window, document, window._fs_namespace, 'script', window._fs_script)
    </script>
  </body>
</html>
