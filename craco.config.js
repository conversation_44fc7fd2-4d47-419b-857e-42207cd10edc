const fs = require('fs')
const path = require('path')
const rewireBabelLoader = require('craco-babel-loader')
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')

const resolve = (file) => path.resolve(__dirname, file)

// Dynamically get ./src directories
const aliases = fs
  .readdirSync(path.resolve(__dirname, 'src'), { withFileTypes: true })
  .filter((item) => item.isDirectory())
  .map(({ name }) => name)

module.exports = {
  reactScriptsVersion: 'react-scripts',
  eslint: {
    enable: true,
  },
  typescript: {
    enableTypeChecking: true,
  },
  webpack: {
    alias: {
      '@root': path.resolve(__dirname, '.'),
      ...aliases.reduce(
        (paths, drectory) => ({
          ...paths,
          [`@/${drectory}`]: path.resolve(__dirname, `./src/${drectory}`),
        }),
        {}
      ),
    },
    plugins: [new NodePolyfillPlugin()],
    configure: (config) => {
      const scopePluginIndex = config.resolve.plugins.findIndex(
        ({ constructor }) => constructor?.name === 'ModuleScopePlugin'
      )

      config.resolve.plugins.splice(scopePluginIndex, 1)

      return { ...config, ignoreWarnings: [/Failed to parse source map/] }
    },
    resolve: {
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
    },
  },
  plugins: [
    {
      plugin: rewireBabelLoader,
      options: {
        includes: [resolve('flint.config.tsx'), /node_modules\/@flint\/*/],
      },
    },
  ],
}
